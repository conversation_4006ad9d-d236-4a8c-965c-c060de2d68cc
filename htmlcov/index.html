<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">28%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-01 11:00 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276___init___py.html">mermaid_render/__init__.py</a></td>
                <td>61</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="49 61">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c___init___py.html">mermaid_render/ai/__init__.py</a></td>
                <td>8</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="1 8">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html">mermaid_render/ai/analysis.py</a></td>
                <td>210</td>
                <td>210</td>
                <td>0</td>
                <td class="right" data-ratio="0 210">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html">mermaid_render/ai/diagram_generator.py</a></td>
                <td>228</td>
                <td>219</td>
                <td>0</td>
                <td class="right" data-ratio="9 228">4%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html">mermaid_render/ai/nl_processor.py</a></td>
                <td>160</td>
                <td>119</td>
                <td>0</td>
                <td class="right" data-ratio="41 160">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html">mermaid_render/ai/optimization.py</a></td>
                <td>93</td>
                <td>93</td>
                <td>0</td>
                <td class="right" data-ratio="0 93">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html">mermaid_render/ai/providers.py</a></td>
                <td>69</td>
                <td>50</td>
                <td>8</td>
                <td class="right" data-ratio="19 69">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html">mermaid_render/ai/suggestions.py</a></td>
                <td>84</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="0 84">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html">mermaid_render/ai/utils.py</a></td>
                <td>81</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="0 81">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a___init___py.html">mermaid_render/cache/__init__.py</a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html">mermaid_render/cache/cache_manager.py</a></td>
                <td>201</td>
                <td>143</td>
                <td>0</td>
                <td class="right" data-ratio="58 201">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html">mermaid_render/cache/optimization.py</a></td>
                <td>235</td>
                <td>197</td>
                <td>12</td>
                <td class="right" data-ratio="38 235">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html">mermaid_render/cache/performance.py</a></td>
                <td>183</td>
                <td>125</td>
                <td>0</td>
                <td class="right" data-ratio="58 183">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html">mermaid_render/cache/storage_backends.py</a></td>
                <td>294</td>
                <td>246</td>
                <td>24</td>
                <td class="right" data-ratio="48 294">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html">mermaid_render/cache/strategies.py</a></td>
                <td>210</td>
                <td>174</td>
                <td>32</td>
                <td class="right" data-ratio="36 210">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html">mermaid_render/cache/utils.py</a></td>
                <td>156</td>
                <td>136</td>
                <td>0</td>
                <td class="right" data-ratio="20 156">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c___init___py.html">mermaid_render/collaboration/__init__.py</a></td>
                <td>8</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="1 8">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html">mermaid_render/collaboration/activity_log.py</a></td>
                <td>101</td>
                <td>101</td>
                <td>0</td>
                <td class="right" data-ratio="0 101">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td>210</td>
                <td>203</td>
                <td>0</td>
                <td class="right" data-ratio="7 210">3%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html">mermaid_render/collaboration/comments.py</a></td>
                <td>119</td>
                <td>119</td>
                <td>0</td>
                <td class="right" data-ratio="0 119">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html">mermaid_render/collaboration/diff_engine.py</a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html">mermaid_render/collaboration/merge_resolver.py</a></td>
                <td>92</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="0 92">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html">mermaid_render/collaboration/utils.py</a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html">mermaid_render/collaboration/version_control.py</a></td>
                <td>198</td>
                <td>198</td>
                <td>0</td>
                <td class="right" data-ratio="0 198">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf___init___py.html">mermaid_render/config/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html">mermaid_render/config/config_manager.py</a></td>
                <td>115</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="106 115">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html">mermaid_render/config/theme_manager.py</a></td>
                <td>94</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="89 94">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html">mermaid_render/core.py</a></td>
                <td>109</td>
                <td>0</td>
                <td>8</td>
                <td class="right" data-ratio="109 109">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html">mermaid_render/exceptions.py</a></td>
                <td>130</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="81 130">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1___init___py.html">mermaid_render/interactive/__init__.py</a></td>
                <td>9</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="2 9">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html">mermaid_render/interactive/builder.py</a></td>
                <td>251</td>
                <td>153</td>
                <td>0</td>
                <td class="right" data-ratio="98 251">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_export_py.html">mermaid_render/interactive/export.py</a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html">mermaid_render/interactive/server.py</a></td>
                <td>160</td>
                <td>153</td>
                <td>0</td>
                <td class="right" data-ratio="7 160">4%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_templates_py.html">mermaid_render/interactive/templates.py</a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html">mermaid_render/interactive/ui_components.py</a></td>
                <td>52</td>
                <td>52</td>
                <td>4</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_utils_py.html">mermaid_render/interactive/utils.py</a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html">mermaid_render/interactive/validation.py</a></td>
                <td>149</td>
                <td>149</td>
                <td>0</td>
                <td class="right" data-ratio="0 149">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html">mermaid_render/interactive/websocket_handler.py</a></td>
                <td>149</td>
                <td>149</td>
                <td>0</td>
                <td class="right" data-ratio="0 149">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602___init___py.html">mermaid_render/models/__init__.py</a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html">mermaid_render/models/class_diagram.py</a></td>
                <td>122</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="122 122">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_er_diagram_py.html">mermaid_render/models/er_diagram.py</a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html">mermaid_render/models/flowchart.py</a></td>
                <td>108</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="108 108">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_gantt_py.html">mermaid_render/models/gantt.py</a></td>
                <td>30</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="9 30">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html">mermaid_render/models/git_graph.py</a></td>
                <td>27</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="9 27">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html">mermaid_render/models/mindmap.py</a></td>
                <td>53</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="12 53">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_pie_chart_py.html">mermaid_render/models/pie_chart.py</a></td>
                <td>20</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="7 20">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html">mermaid_render/models/sequence.py</a></td>
                <td>123</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="123 123">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_state_py.html">mermaid_render/models/state.py</a></td>
                <td>26</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="9 26">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_user_journey_py.html">mermaid_render/models/user_journey.py</a></td>
                <td>23</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="8 23">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0___init___py.html">mermaid_render/renderers/__init__.py</a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html">mermaid_render/renderers/pdf_renderer.py</a></td>
                <td>58</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 58">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html">mermaid_render/renderers/png_renderer.py</a></td>
                <td>51</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html">mermaid_render/renderers/svg_renderer.py</a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b___init___py.html">mermaid_render/templates/__init__.py</a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html">mermaid_render/templates/data_sources.py</a></td>
                <td>252</td>
                <td>216</td>
                <td>8</td>
                <td class="right" data-ratio="36 252">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html">mermaid_render/templates/generators.py</a></td>
                <td>187</td>
                <td>166</td>
                <td>8</td>
                <td class="right" data-ratio="21 187">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html">mermaid_render/templates/library.py</a></td>
                <td>36</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="16 36">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html">mermaid_render/templates/schema.py</a></td>
                <td>182</td>
                <td>141</td>
                <td>0</td>
                <td class="right" data-ratio="41 182">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html">mermaid_render/templates/template_manager.py</a></td>
                <td>154</td>
                <td>109</td>
                <td>0</td>
                <td class="right" data-ratio="45 154">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html">mermaid_render/templates/utils.py</a></td>
                <td>142</td>
                <td>125</td>
                <td>0</td>
                <td class="right" data-ratio="17 142">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258___init___py.html">mermaid_render/utils/__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_export_py.html">mermaid_render/utils/export.py</a></td>
                <td>65</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="59 65">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html">mermaid_render/utils/helpers.py</a></td>
                <td>63</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="48 63">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_validation_py.html">mermaid_render/utils/validation.py</a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93___init___py.html">mermaid_render/validators/__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html">mermaid_render/validators/validator.py</a></td>
                <td>190</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="175 190">92%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>6438</td>
                <td>4616</td>
                <td>104</td>
                <td class="right" data-ratio="1822 6438">28%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-01 11:00 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_9bf3ad49003d1e93_validator_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_f14f9617df6ea276___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
