<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">28%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-01 11:00 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276___init___py.html">mermaid_render/__init__.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>61</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="49 61">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c___init___py.html">mermaid_render/ai/__init__.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="1 8">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t10">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t10"><data value='ComplexityAnalysis'>ComplexityAnalysis</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t32">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t32"><data value='QualityMetrics'>QualityMetrics</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t52">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t52"><data value='AnalysisReport'>AnalysisReport</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t75">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t75"><data value='DiagramAnalyzer'>DiagramAnalyzer</data></a></td>
                <td>146</td>
                <td>146</td>
                <td>0</td>
                <td class="right" data-ratio="0 146">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t20">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t20"><data value='DiagramType'>DiagramType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t34">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t34"><data value='GenerationConfig'>GenerationConfig</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t61">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t61"><data value='GenerationResult'>GenerationResult</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t83">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t83"><data value='DiagramGenerator'>DiagramGenerator</data></a></td>
                <td>159</td>
                <td>159</td>
                <td>0</td>
                <td class="right" data-ratio="0 159">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>67</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="9 67">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t15">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t15"><data value='EntityExtraction'>EntityExtraction</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t31">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t31"><data value='IntentClassification'>IntentClassification</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t47">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t47"><data value='TextAnalysis'>TextAnalysis</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t72">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t72"><data value='NLProcessor'>NLProcessor</data></a></td>
                <td>116</td>
                <td>116</td>
                <td>0</td>
                <td class="right" data-ratio="0 116">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="41 41">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t8">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t8"><data value='OptimizationType'>OptimizationType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t17">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t17"><data value='OptimizationResult'>OptimizationResult</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t36">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t36"><data value='LayoutOptimizer'>LayoutOptimizer</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t92">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t92"><data value='StyleOptimizer'>StyleOptimizer</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t138">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t138"><data value='DiagramOptimizer'>DiagramOptimizer</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t8">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t8"><data value='AIProvider'>AIProvider</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t22">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t22"><data value='OpenAIProvider'>OpenAIProvider</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t75">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t75"><data value='AnthropicProvider'>AnthropicProvider</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t117">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t117"><data value='LocalModelProvider'>LocalModelProvider</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t9">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t9"><data value='SuggestionType'>SuggestionType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t19">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t19"><data value='SuggestionPriority'>SuggestionPriority</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t28">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t28"><data value='Suggestion'>Suggestion</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t61">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t61"><data value='SuggestionEngine'>SuggestionEngine</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html">mermaid_render/ai/utils.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>81</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="0 81">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a___init___py.html">mermaid_render/cache/__init__.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t24">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t24"><data value='CacheKeyType'>CacheKeyType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t34">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t34"><data value='CacheKey'>CacheKey</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t99">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t99"><data value='CacheEntry'>CacheEntry</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t153">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t153"><data value='CacheManager'>CacheManager</data></a></td>
                <td>111</td>
                <td>111</td>
                <td>0</td>
                <td class="right" data-ratio="0 111">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>58</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="58 58">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t22">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t22"><data value='CacheOptimizer'>CacheOptimizer</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t39">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t39"><data value='CompressionOptimizer'>CompressionOptimizer</data></a></td>
                <td>60</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="0 60">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t179">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t179"><data value='PrefetchOptimizer'>PrefetchOptimizer</data></a></td>
                <td>60</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="0 60">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t328">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t328"><data value='WarmupOptimizer'>WarmupOptimizer</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t422">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t422"><data value='AdaptiveOptimizer'>AdaptiveOptimizer</data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t18">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t18"><data value='RenderingMetrics'>RenderingMetrics</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t45">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t45"><data value='CacheMetrics'>CacheMetrics</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t70">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t70"><data value='PerformanceReport'>PerformanceReport</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t104">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t104"><data value='PerformanceMonitor'>PerformanceMonitor</data></a></td>
                <td>122</td>
                <td>122</td>
                <td>0</td>
                <td class="right" data-ratio="0 122">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>58</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="58 58">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t21">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t21"><data value='CacheBackend'>CacheBackend</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>12</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t64">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t64"><data value='MemoryBackend'>MemoryBackend</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t123">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t123"><data value='FileBackend'>FileBackend</data></a></td>
                <td>109</td>
                <td>109</td>
                <td>0</td>
                <td class="right" data-ratio="0 109">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t348">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t348"><data value='RedisBackend'>RedisBackend</data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t482">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t482"><data value='CompositeCacheBackend'>CompositeCacheBackend</data></a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>48</td>
                <td>0</td>
                <td>12</td>
                <td class="right" data-ratio="48 48">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t16">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t16"><data value='EvictionCandidate'>EvictionCandidate</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t39">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t39"><data value='CacheStrategy'>CacheStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>23</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t77">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t77"><data value='TTLStrategy'>TTLStrategy</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t148">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t148"><data value='LRUStrategy'>LRUStrategy</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t223">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t223"><data value='SizeBasedStrategy'>SizeBasedStrategy</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t320">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t320"><data value='SmartStrategy'>SmartStrategy</data></a></td>
                <td>84</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="0 84">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>0</td>
                <td>9</td>
                <td class="right" data-ratio="36 36">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html">mermaid_render/cache/utils.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>156</td>
                <td>136</td>
                <td>0</td>
                <td class="right" data-ratio="20 156">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c___init___py.html">mermaid_render/collaboration/__init__.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="1 8">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t10">mermaid_render/collaboration/activity_log.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t10"><data value='ActivityType'>ActivityType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t32">mermaid_render/collaboration/activity_log.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t32"><data value='Activity'>Activity</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t62">mermaid_render/collaboration/activity_log.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t62"><data value='AuditTrail'>AuditTrail</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t86">mermaid_render/collaboration/activity_log.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t86"><data value='ActivityLogger'>ActivityLogger</data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html">mermaid_render/collaboration/activity_log.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t18">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t18"><data value='Permission'>Permission</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t26">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t26"><data value='SessionState'>SessionState</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t35">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t35"><data value='Collaborator'>Collaborator</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t88">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t88"><data value='CollaborativeSession'>CollaborativeSession</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t165">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t165"><data value='CollaborationManager'>CollaborationManager</data></a></td>
                <td>106</td>
                <td>106</td>
                <td>0</td>
                <td class="right" data-ratio="0 106">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>70</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="7 70">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t10">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t10"><data value='ReviewStatus'>ReviewStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t19">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t19"><data value='Comment'>Comment</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t53">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t53"><data value='CommentThread'>CommentThread</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t79">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t79"><data value='Review'>Review</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t113">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t113"><data value='CommentSystem'>CommentSystem</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t8">mermaid_render/collaboration/diff_engine.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t8"><data value='ChangeType'>ChangeType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t17">mermaid_render/collaboration/diff_engine.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t17"><data value='Change'>Change</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t37">mermaid_render/collaboration/diff_engine.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t37"><data value='DiagramDiff'>DiagramDiff</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t51">mermaid_render/collaboration/diff_engine.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t51"><data value='ConflictResolution'>ConflictResolution</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t66">mermaid_render/collaboration/diff_engine.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t66"><data value='DiffEngine'>DiffEngine</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html">mermaid_render/collaboration/diff_engine.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t10">mermaid_render/collaboration/merge_resolver.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t10"><data value='MergeStrategy'>MergeStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t19">mermaid_render/collaboration/merge_resolver.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t19"><data value='MergeConflict'>MergeConflict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t38">mermaid_render/collaboration/merge_resolver.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t38"><data value='ConflictResolver'>ConflictResolver</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t136">mermaid_render/collaboration/merge_resolver.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t136"><data value='MergeResolver'>MergeResolver</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html">mermaid_render/collaboration/merge_resolver.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html">mermaid_render/collaboration/utils.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t18">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t18"><data value='MergeStatus'>MergeStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t26">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t26"><data value='ChangeSet'>ChangeSet</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t55">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t55"><data value='Commit'>Commit</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t90">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t90"><data value='Branch'>Branch</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t115">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t115"><data value='DiagramVersion'>DiagramVersion</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t136">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t136"><data value='MergeResult'>MergeResult</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t156">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t156"><data value='VersionControl'>VersionControl</data></a></td>
                <td>113</td>
                <td>113</td>
                <td>0</td>
                <td class="right" data-ratio="0 113">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>76</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 76">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf___init___py.html">mermaid_render/config/__init__.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t15">mermaid_render/config/config_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t15"><data value='ConfigManager'>ConfigManager</data></a></td>
                <td>93</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="84 93">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html">mermaid_render/config/config_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t14">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t14"><data value='ThemeManager'>ThemeManager</data></a></td>
                <td>73</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="68 73">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t25">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t25"><data value='MermaidConfig'>MermaidConfig</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t68">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t68"><data value='MermaidTheme'>MermaidTheme</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t114">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t114"><data value='MermaidDiagram'>MermaidDiagram</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t168">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t168"><data value='MermaidRenderer'>MermaidRenderer</data></a></td>
                <td>50</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="50 50">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t11">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t11"><data value='MermaidRenderError'>MermaidRenderError</data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t38">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t38"><data value='ValidationError'>ValidationError</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t79">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t79"><data value='RenderingError'>RenderingError</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t120">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t120"><data value='ConfigurationError'>ConfigurationError</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t161">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t161"><data value='UnsupportedFormatError'>UnsupportedFormatError</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t201">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t201"><data value='NetworkError'>NetworkError</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t242">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t242"><data value='ThemeError'>ThemeError</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t283">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t283"><data value='DiagramError'>DiagramError</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t324">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t324"><data value='TemplateError'>TemplateError</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t366">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t366"><data value='DataSourceError'>DataSourceError</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t408">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t408"><data value='CacheError'>CacheError</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1___init___py.html">mermaid_render/interactive/__init__.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="2 9">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t20">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t20"><data value='ElementType'>ElementType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t28">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t28"><data value='DiagramType'>DiagramType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t38">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t38"><data value='Position'>Position</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t52">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t52"><data value='Size'>Size</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t66">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t66"><data value='DiagramElement'>DiagramElement</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t144">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t144"><data value='DiagramConnection'>DiagramConnection</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t214">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t214"><data value='DiagramBuilder'>DiagramBuilder</data></a></td>
                <td>125</td>
                <td>125</td>
                <td>0</td>
                <td class="right" data-ratio="0 125">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>98</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="98 98">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_export_py.html#t7">mermaid_render/interactive/export.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_export_py.html#t7"><data value='ExportFormat'>ExportFormat</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_export_py.html#t16">mermaid_render/interactive/export.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_export_py.html#t16"><data value='ExportManager'>ExportManager</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_export_py.html">mermaid_render/interactive/export.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_export_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t28">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t28"><data value='InteractiveServer'>InteractiveServer</data></a></td>
                <td>134</td>
                <td>134</td>
                <td>0</td>
                <td class="right" data-ratio="0 134">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="7 26">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_templates_py.html#t8">mermaid_render/interactive/templates.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_templates_py.html#t8"><data value='InteractiveTemplate'>InteractiveTemplate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_templates_py.html#t20">mermaid_render/interactive/templates.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_templates_py.html#t20"><data value='TemplateLibrary'>TemplateLibrary</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_templates_py.html">mermaid_render/interactive/templates.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_templates_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t14">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t14"><data value='UIComponent'>UIComponent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t28">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t28"><data value='NodeComponent'>NodeComponent</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t48">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t48"><data value='EdgeComponent'>EdgeComponent</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t67">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t67"><data value='ToolboxComponent'>ToolboxComponent</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t96">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t96"><data value='PropertiesPanel'>PropertiesPanel</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t119">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t119"><data value='CodeEditor'>CodeEditor</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t143">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t143"><data value='PreviewPanel'>PreviewPanel</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>32</td>
                <td>2</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_utils_py.html">mermaid_render/interactive/utils.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t16">mermaid_render/interactive/validation.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t16"><data value='ValidationIssue'>ValidationIssue</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t39">mermaid_render/interactive/validation.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t39"><data value='ValidationResult'>ValidationResult</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t57">mermaid_render/interactive/validation.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t57"><data value='LiveValidator'>LiveValidator</data></a></td>
                <td>117</td>
                <td>117</td>
                <td>0</td>
                <td class="right" data-ratio="0 117">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html">mermaid_render/interactive/validation.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t19">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t19"><data value='DiagramSession'>DiagramSession</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t50">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t50"><data value='WebSocketHandler'>WebSocketHandler</data></a></td>
                <td>105</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="0 105">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602___init___py.html">mermaid_render/models/__init__.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t13">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t13"><data value='ClassMethod'>ClassMethod</data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t52">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t52"><data value='ClassAttribute'>ClassAttribute</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t82">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t82"><data value='ClassDefinition'>ClassDefinition</data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t138">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t138"><data value='ClassRelationship'>ClassRelationship</data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t193">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t193"><data value='ClassDiagram'>ClassDiagram</data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_er_diagram_py.html#t8">mermaid_render/models/er_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_er_diagram_py.html#t8"><data value='ERDiagram'>ERDiagram</data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_er_diagram_py.html">mermaid_render/models/er_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_er_diagram_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t13">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t13"><data value='FlowchartNode'>FlowchartNode</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t62">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t62"><data value='FlowchartEdge'>FlowchartEdge</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t112">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t112"><data value='FlowchartSubgraph'>FlowchartSubgraph</data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t163">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t163"><data value='FlowchartDiagram'>FlowchartDiagram</data></a></td>
                <td>48</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="48 48">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_gantt_py.html#t8">mermaid_render/models/gantt.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_gantt_py.html#t8"><data value='GanttDiagram'>GanttDiagram</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_gantt_py.html">mermaid_render/models/gantt.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_gantt_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html#t7">mermaid_render/models/git_graph.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html#t7"><data value='GitGraphDiagram'>GitGraphDiagram</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html">mermaid_render/models/git_graph.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t7">mermaid_render/models/mindmap.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t7"><data value='MindmapNode'>MindmapNode</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t42">mermaid_render/models/mindmap.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t42"><data value='MindmapDiagram'>MindmapDiagram</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html">mermaid_render/models/mindmap.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_pie_chart_py.html#t7">mermaid_render/models/pie_chart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_pie_chart_py.html#t7"><data value='PieChartDiagram'>PieChartDiagram</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_pie_chart_py.html">mermaid_render/models/pie_chart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_pie_chart_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t13">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t13"><data value='SequenceParticipant'>SequenceParticipant</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t34">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t34"><data value='SequenceMessage'>SequenceMessage</data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t97">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t97"><data value='SequenceNote'>SequenceNote</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t135">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t135"><data value='SequenceLoop'>SequenceLoop</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t173">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t173"><data value='SequenceDiagram'>SequenceDiagram</data></a></td>
                <td>50</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="50 50">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_state_py.html#t8">mermaid_render/models/state.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_state_py.html#t8"><data value='StateDiagram'>StateDiagram</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_state_py.html">mermaid_render/models/state.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_state_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_user_journey_py.html#t7">mermaid_render/models/user_journey.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_user_journey_py.html#t7"><data value='UserJourneyDiagram'>UserJourneyDiagram</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_user_journey_py.html">mermaid_render/models/user_journey.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_user_journey_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0___init___py.html">mermaid_render/renderers/__init__.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t12">mermaid_render/renderers/pdf_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t12"><data value='PDFRenderer'>PDFRenderer</data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html">mermaid_render/renderers/pdf_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html#t14">mermaid_render/renderers/png_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html#t14"><data value='PNGRenderer'>PNGRenderer</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html">mermaid_render/renderers/png_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html#t15">mermaid_render/renderers/svg_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html#t15"><data value='SVGRenderer'>SVGRenderer</data></a></td>
                <td>51</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html">mermaid_render/renderers/svg_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b___init___py.html">mermaid_render/templates/__init__.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t20">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t20"><data value='DataSource'>DataSource</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t34">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t34"><data value='JSONDataSource'>JSONDataSource</data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t151">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t151"><data value='CSVDataSource'>CSVDataSource</data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t257">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t257"><data value='DatabaseDataSource'>DatabaseDataSource</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t370">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t370"><data value='APIDataSource'>APIDataSource</data></a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>18</td>
                <td>4</td>
                <td class="right" data-ratio="36 54">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t16">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t16"><data value='DiagramGenerator'>DiagramGenerator</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t30">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t30"><data value='FlowchartGenerator'>FlowchartGenerator</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t199">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t199"><data value='SequenceGenerator'>SequenceGenerator</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t316">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t316"><data value='ClassDiagramGenerator'>ClassDiagramGenerator</data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t477">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t477"><data value='ArchitectureGenerator'>ArchitectureGenerator</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t569">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t569"><data value='ProcessFlowGenerator'>ProcessFlowGenerator</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t13">mermaid_render/templates/library.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t13"><data value='BuiltInTemplates'>BuiltInTemplates</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t329">mermaid_render/templates/library.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t329"><data value='CommunityTemplates'>CommunityTemplates</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html">mermaid_render/templates/library.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t16">mermaid_render/templates/schema.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t16"><data value='ParameterType'>ParameterType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t28">mermaid_render/templates/schema.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t28"><data value='ParameterSchema'>ParameterSchema</data></a></td>
                <td>60</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="0 60">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t153">mermaid_render/templates/schema.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t153"><data value='TemplateSchema'>TemplateSchema</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html">mermaid_render/templates/schema.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>92</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="41 92">45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t23">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t23"><data value='Template'>Template</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t78">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t78"><data value='TemplateManager'>TemplateManager</data></a></td>
                <td>92</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="0 92">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="45 45">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html">mermaid_render/templates/utils.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>142</td>
                <td>125</td>
                <td>0</td>
                <td class="right" data-ratio="17 142">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258___init___py.html">mermaid_render/utils/__init__.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_export_py.html">mermaid_render/utils/export.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_export_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>65</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="59 65">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html">mermaid_render/utils/helpers.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>63</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="48 63">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_validation_py.html">mermaid_render/utils/validation.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93___init___py.html">mermaid_render/validators/__init__.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t14">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t14"><data value='ValidationResult'>ValidationResult</data></a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t41">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t41"><data value='MermaidValidator'>MermaidValidator</data></a></td>
                <td>149</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="135 149">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>6438</td>
                <td>4616</td>
                <td>104</td>
                <td class="right" data-ratio="1822 6438">28%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-01 11:00 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
