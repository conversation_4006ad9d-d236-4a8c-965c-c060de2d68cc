"""
Cache storage backends for different storage mechanisms.

This module provides various storage backends for the cache system,
including memory, file-based, Redis, and composite backends.
"""

import json
import pickle
import sqlite3
import threading
import time
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, Any, Optional, List, Iterator
from datetime import datetime

from ..exceptions import CacheError


class CacheBackend(ABC):
    """Abstract base class for cache storage backends."""
    
    @abstractmethod
    def get(self, key: str) -> Optional['CacheEntry']:
        """Get cache entry by key."""
        pass
    
    @abstractmethod
    def put(self, key: str, entry: 'CacheEntry') -> None:
        """Store cache entry."""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """Delete cache entry."""
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """Clear all cache entries."""
        pass
    
    @abstractmethod
    def keys(self) -> Iterator[str]:
        """Get all cache keys."""
        pass
    
    @abstractmethod
    def size(self) -> int:
        """Get number of entries."""
        pass
    
    def get_all_entries(self) -> List['CacheEntry']:
        """Get all cache entries."""
        entries = []
        for key in self.keys():
            entry = self.get(key)
            if entry:
                entries.append(entry)
        return entries


class MemoryBackend(CacheBackend):
    """
    In-memory cache backend.
    
    Fast access but limited by available memory and not persistent
    across application restarts.
    """
    
    def __init__(self, max_entries: int = 10000):
        """
        Initialize memory backend.
        
        Args:
            max_entries: Maximum number of entries to store
        """
        self.max_entries = max_entries
        self._cache: Dict[str, 'CacheEntry'] = {}
        self._lock = threading.RLock()
    
    def get(self, key: str) -> Optional['CacheEntry']:
        """Get cache entry by key."""
        with self._lock:
            return self._cache.get(key)
    
    def put(self, key: str, entry: 'CacheEntry') -> None:
        """Store cache entry."""
        with self._lock:
            # Check size limit
            if len(self._cache) >= self.max_entries and key not in self._cache:
                # Remove oldest entry (simple FIFO)
                oldest_key = next(iter(self._cache))
                del self._cache[oldest_key]
            
            self._cache[key] = entry
    
    def delete(self, key: str) -> bool:
        """Delete cache entry."""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """Clear all cache entries."""
        with self._lock:
            self._cache.clear()
    
    def keys(self) -> Iterator[str]:
        """Get all cache keys."""
        with self._lock:
            return iter(list(self._cache.keys()))
    
    def size(self) -> int:
        """Get number of entries."""
        with self._lock:
            return len(self._cache)


class FileBackend(CacheBackend):
    """
    File-based cache backend.
    
    Persistent storage using SQLite database for metadata
    and separate files for content.
    """
    
    def __init__(self, cache_dir: Path, max_size_mb: int = 1000):
        """
        Initialize file backend.
        
        Args:
            cache_dir: Directory for cache files
            max_size_mb: Maximum cache size in MB
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_size_mb = max_size_mb
        
        # Initialize SQLite database for metadata
        self.db_path = self.cache_dir / "cache.db"
        self._init_database()
        self._lock = threading.RLock()
    
    def _init_database(self) -> None:
        """Initialize SQLite database."""
        conn = sqlite3.connect(self.db_path)
        conn.execute("""
            CREATE TABLE IF NOT EXISTS cache_entries (
                key TEXT PRIMARY KEY,
                content_path TEXT,
                content_type TEXT,
                size_bytes INTEGER,
                created_at TEXT,
                accessed_at TEXT,
                access_count INTEGER,
                ttl_seconds INTEGER,
                tags TEXT,
                metadata TEXT
            )
        """)
        conn.commit()
        conn.close()
    
    def get(self, key: str) -> Optional['CacheEntry']:
        """Get cache entry by key."""
        with self._lock:
            try:
                conn = sqlite3.connect(self.db_path)
                conn.row_factory = sqlite3.Row
                
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM cache_entries WHERE key = ?", (key,))
                row = cursor.fetchone()
                conn.close()
                
                if not row:
                    return None
                
                # Load content from file
                content_path = self.cache_dir / row['content_path']
                if not content_path.exists():
                    # Content file missing, remove from database
                    self.delete(key)
                    return None
                
                # Load content based on type
                if row['content_type'] == 'text':
                    with open(content_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                elif row['content_type'] == 'binary':
                    with open(content_path, 'rb') as f:
                        content = f.read()
                else:  # object
                    with open(content_path, 'rb') as f:
                        content = pickle.load(f)
                
                # Reconstruct cache entry
                from .cache_manager import CacheEntry
                entry = CacheEntry(
                    key=row['key'],
                    content=content,
                    content_type=row['content_type'],
                    size_bytes=row['size_bytes'],
                    created_at=datetime.fromisoformat(row['created_at']),
                    accessed_at=datetime.fromisoformat(row['accessed_at']),
                    access_count=row['access_count'],
                    ttl_seconds=row['ttl_seconds'],
                    tags=json.loads(row['tags']) if row['tags'] else [],
                    metadata=json.loads(row['metadata']) if row['metadata'] else {},
                )
                
                return entry
                
            except Exception as e:
                raise CacheError(f"Failed to get cache entry: {str(e)}") from e
    
    def put(self, key: str, entry: 'CacheEntry') -> None:
        """Store cache entry."""
        with self._lock:
            try:
                # Generate content file path
                content_filename = f"{hash(key) % 1000000:06d}.cache"
                content_path = self.cache_dir / content_filename
                
                # Save content to file
                if entry.content_type == 'text':
                    with open(content_path, 'w', encoding='utf-8') as f:
                        f.write(entry.content)
                elif entry.content_type == 'binary':
                    with open(content_path, 'wb') as f:
                        f.write(entry.content)
                else:  # object
                    with open(content_path, 'wb') as f:
                        pickle.dump(entry.content, f)
                
                # Save metadata to database
                conn = sqlite3.connect(self.db_path)
                conn.execute("""
                    INSERT OR REPLACE INTO cache_entries 
                    (key, content_path, content_type, size_bytes, created_at, 
                     accessed_at, access_count, ttl_seconds, tags, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    entry.key,
                    content_filename,
                    entry.content_type,
                    entry.size_bytes,
                    entry.created_at.isoformat(),
                    entry.accessed_at.isoformat(),
                    entry.access_count,
                    entry.ttl_seconds,
                    json.dumps(entry.tags),
                    json.dumps(entry.metadata),
                ))
                conn.commit()
                conn.close()
                
            except Exception as e:
                # Clean up content file if database operation failed
                if content_path.exists():
                    content_path.unlink()
                raise CacheError(f"Failed to put cache entry: {str(e)}") from e
    
    def delete(self, key: str) -> bool:
        """Delete cache entry."""
        with self._lock:
            try:
                conn = sqlite3.connect(self.db_path)
                conn.row_factory = sqlite3.Row
                
                # Get content path before deletion
                cursor = conn.cursor()
                cursor.execute("SELECT content_path FROM cache_entries WHERE key = ?", (key,))
                row = cursor.fetchone()
                
                if row:
                    # Delete from database
                    conn.execute("DELETE FROM cache_entries WHERE key = ?", (key,))
                    conn.commit()
                    
                    # Delete content file
                    content_path = self.cache_dir / row['content_path']
                    if content_path.exists():
                        content_path.unlink()
                    
                    conn.close()
                    return True
                
                conn.close()
                return False
                
            except Exception as e:
                raise CacheError(f"Failed to delete cache entry: {str(e)}") from e
    
    def clear(self) -> None:
        """Clear all cache entries."""
        with self._lock:
            try:
                # Get all content paths
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT content_path FROM cache_entries")
                rows = cursor.fetchall()
                
                # Delete all content files
                for row in rows:
                    content_path = self.cache_dir / row[0]
                    if content_path.exists():
                        content_path.unlink()
                
                # Clear database
                conn.execute("DELETE FROM cache_entries")
                conn.commit()
                conn.close()
                
            except Exception as e:
                raise CacheError(f"Failed to clear cache: {str(e)}") from e
    
    def keys(self) -> Iterator[str]:
        """Get all cache keys."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT key FROM cache_entries")
            keys = [row[0] for row in cursor.fetchall()]
            conn.close()
            return iter(keys)
        except Exception as e:
            raise CacheError(f"Failed to get cache keys: {str(e)}") from e
    
    def size(self) -> int:
        """Get number of entries."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM cache_entries")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except Exception as e:
            raise CacheError(f"Failed to get cache size: {str(e)}") from e


class RedisBackend(CacheBackend):
    """
    Redis cache backend.
    
    Distributed caching using Redis with support for clustering
    and high availability.
    """
    
    def __init__(
        self,
        host: str = 'localhost',
        port: int = 6379,
        db: int = 0,
        password: Optional[str] = None,
        key_prefix: str = 'mermaid_cache:',
        serialization: str = 'pickle',
    ):
        """
        Initialize Redis backend.
        
        Args:
            host: Redis host
            port: Redis port
            db: Redis database number
            password: Redis password
            key_prefix: Prefix for cache keys
            serialization: Serialization method (pickle, json)
        """
        try:
            import redis
        except ImportError:
            raise CacheError("Redis backend requires 'redis' package")
        
        self.redis_client = redis.Redis(
            host=host,
            port=port,
            db=db,
            password=password,
            decode_responses=False,
        )
        self.key_prefix = key_prefix
        self.serialization = serialization
    
    def _serialize(self, entry: 'CacheEntry') -> bytes:
        """Serialize cache entry."""
        if self.serialization == 'pickle':
            return pickle.dumps(entry)
        elif self.serialization == 'json':
            return json.dumps(entry.to_dict()).encode('utf-8')
        else:
            raise CacheError(f"Unsupported serialization: {self.serialization}")
    
    def _deserialize(self, data: bytes) -> 'CacheEntry':
        """Deserialize cache entry."""
        if self.serialization == 'pickle':
            return pickle.loads(data)
        elif self.serialization == 'json':
            from .cache_manager import CacheEntry
            entry_data = json.loads(data.decode('utf-8'))
            return CacheEntry.from_dict(entry_data)
        else:
            raise CacheError(f"Unsupported serialization: {self.serialization}")
    
    def _make_key(self, key: str) -> str:
        """Create Redis key with prefix."""
        return f"{self.key_prefix}{key}"
    
    def get(self, key: str) -> Optional['CacheEntry']:
        """Get cache entry by key."""
        try:
            redis_key = self._make_key(key)
            data = self.redis_client.get(redis_key)
            
            if data is None:
                return None
            
            return self._deserialize(data)
            
        except Exception as e:
            raise CacheError(f"Failed to get from Redis: {str(e)}") from e
    
    def put(self, key: str, entry: 'CacheEntry') -> None:
        """Store cache entry."""
        try:
            redis_key = self._make_key(key)
            data = self._serialize(entry)
            
            if entry.ttl_seconds:
                self.redis_client.setex(redis_key, entry.ttl_seconds, data)
            else:
                self.redis_client.set(redis_key, data)
                
        except Exception as e:
            raise CacheError(f"Failed to put to Redis: {str(e)}") from e
    
    def delete(self, key: str) -> bool:
        """Delete cache entry."""
        try:
            redis_key = self._make_key(key)
            result = self.redis_client.delete(redis_key)
            return result > 0
        except Exception as e:
            raise CacheError(f"Failed to delete from Redis: {str(e)}") from e
    
    def clear(self) -> None:
        """Clear all cache entries."""
        try:
            pattern = f"{self.key_prefix}*"
            keys = self.redis_client.keys(pattern)
            if keys:
                self.redis_client.delete(*keys)
        except Exception as e:
            raise CacheError(f"Failed to clear Redis cache: {str(e)}") from e
    
    def keys(self) -> Iterator[str]:
        """Get all cache keys."""
        try:
            pattern = f"{self.key_prefix}*"
            redis_keys = self.redis_client.keys(pattern)
            # Remove prefix from keys
            prefix_len = len(self.key_prefix)
            return iter([key.decode('utf-8')[prefix_len:] for key in redis_keys])
        except Exception as e:
            raise CacheError(f"Failed to get Redis keys: {str(e)}") from e
    
    def size(self) -> int:
        """Get number of entries."""
        try:
            pattern = f"{self.key_prefix}*"
            return len(self.redis_client.keys(pattern))
        except Exception as e:
            raise CacheError(f"Failed to get Redis cache size: {str(e)}") from e


class CompositeCacheBackend(CacheBackend):
    """
    Composite cache backend with multiple levels.
    
    Implements a multi-level cache hierarchy (e.g., memory + file + Redis)
    for optimal performance and reliability.
    """
    
    def __init__(self, backends: List[CacheBackend]):
        """
        Initialize composite backend.
        
        Args:
            backends: List of backends in order of preference (fastest first)
        """
        if not backends:
            raise CacheError("At least one backend required")
        
        self.backends = backends
    
    def get(self, key: str) -> Optional['CacheEntry']:
        """Get cache entry, checking backends in order."""
        for i, backend in enumerate(self.backends):
            try:
                entry = backend.get(key)
                if entry is not None:
                    # Populate higher-level caches
                    for j in range(i):
                        try:
                            self.backends[j].put(key, entry)
                        except Exception:
                            # Ignore errors in cache population
                            pass
                    return entry
            except Exception:
                # Continue to next backend on error
                continue
        
        return None
    
    def put(self, key: str, entry: 'CacheEntry') -> None:
        """Store cache entry in all backends."""
        errors = []
        
        for backend in self.backends:
            try:
                backend.put(key, entry)
            except Exception as e:
                errors.append(str(e))
        
        # If all backends failed, raise error
        if len(errors) == len(self.backends):
            raise CacheError(f"All backends failed: {'; '.join(errors)}")
    
    def delete(self, key: str) -> bool:
        """Delete cache entry from all backends."""
        deleted = False
        
        for backend in self.backends:
            try:
                if backend.delete(key):
                    deleted = True
            except Exception:
                # Continue deleting from other backends
                pass
        
        return deleted
    
    def clear(self) -> None:
        """Clear all backends."""
        for backend in self.backends:
            try:
                backend.clear()
            except Exception:
                # Continue clearing other backends
                pass
    
    def keys(self) -> Iterator[str]:
        """Get keys from first available backend."""
        for backend in self.backends:
            try:
                return backend.keys()
            except Exception:
                continue
        
        return iter([])
    
    def size(self) -> int:
        """Get size from first available backend."""
        for backend in self.backends:
            try:
                return backend.size()
            except Exception:
                continue
        
        return 0
