"""
Cache eviction and management strategies.

This module provides various strategies for cache management including
eviction policies, TTL management, and intelligent caching decisions.
"""

import time
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass


@dataclass
class EvictionCandidate:
    """Represents a cache entry candidate for eviction."""
    
    key: str
    size_bytes: int
    created_at: datetime
    accessed_at: datetime
    access_count: int
    score: float  # Lower score = higher priority for eviction
    
    @classmethod
    def from_cache_entry(cls, entry: 'CacheEntry') -> 'EvictionCandidate':
        """Create eviction candidate from cache entry."""
        return cls(
            key=entry.key,
            size_bytes=entry.size_bytes,
            created_at=entry.created_at,
            accessed_at=entry.accessed_at,
            access_count=entry.access_count,
            score=0.0,  # Will be calculated by strategy
        )


class CacheStrategy(ABC):
    """Abstract base class for cache management strategies."""
    
    @abstractmethod
    def select_for_eviction(
        self,
        entries: List['CacheEntry'],
        required_space: int,
        available_space: int,
    ) -> List['CacheEntry']:
        """
        Select cache entries for eviction.
        
        Args:
            entries: List of all cache entries
            required_space: Space needed for new entry
            available_space: Currently available space
            
        Returns:
            List of entries to evict
        """
        pass
    
    @abstractmethod
    def should_cache(self, content_size: int, metadata: Dict[str, Any]) -> bool:
        """
        Determine if content should be cached.
        
        Args:
            content_size: Size of content in bytes
            metadata: Content metadata
            
        Returns:
            True if content should be cached
        """
        pass


class TTLStrategy(CacheStrategy):
    """
    Time-To-Live based cache strategy.
    
    Evicts entries based on expiration time and age,
    with preference for expired and oldest entries.
    """
    
    def __init__(self, default_ttl: int = 3600, max_entry_size_mb: int = 10):
        """
        Initialize TTL strategy.
        
        Args:
            default_ttl: Default TTL in seconds
            max_entry_size_mb: Maximum size for individual entries
        """
        self.default_ttl = default_ttl
        self.max_entry_size_mb = max_entry_size_mb
    
    def select_for_eviction(
        self,
        entries: List['CacheEntry'],
        required_space: int,
        available_space: int,
    ) -> List['CacheEntry']:
        """Select entries for eviction based on TTL and age."""
        if not entries:
            return []
        
        candidates = []
        now = datetime.now()
        
        for entry in entries:
            candidate = EvictionCandidate.from_cache_entry(entry)
            
            # Calculate score based on expiration and age
            if entry.is_expired():
                candidate.score = 0.0  # Highest priority for eviction
            else:
                # Score based on age (older = higher score for eviction)
                age_seconds = (now - entry.created_at).total_seconds()
                ttl_seconds = entry.ttl_seconds or self.default_ttl
                age_ratio = age_seconds / ttl_seconds
                candidate.score = age_ratio
            
            candidates.append(candidate)
        
        # Sort by score (lowest first = highest priority for eviction)
        candidates.sort(key=lambda c: c.score)
        
        # Select entries to evict
        evicted = []
        freed_space = 0
        
        for candidate in candidates:
            if freed_space >= required_space:
                break
            
            # Find corresponding entry
            entry = next(e for e in entries if e.key == candidate.key)
            evicted.append(entry)
            freed_space += entry.size_bytes
        
        return evicted
    
    def should_cache(self, content_size: int, metadata: Dict[str, Any]) -> bool:
        """Determine if content should be cached based on size."""
        max_size_bytes = self.max_entry_size_mb * 1024 * 1024
        return content_size <= max_size_bytes


class LRUStrategy(CacheStrategy):
    """
    Least Recently Used cache strategy.
    
    Evicts entries that haven't been accessed recently,
    with consideration for access frequency.
    """
    
    def __init__(self, max_entry_size_mb: int = 10, frequency_weight: float = 0.3):
        """
        Initialize LRU strategy.
        
        Args:
            max_entry_size_mb: Maximum size for individual entries
            frequency_weight: Weight for access frequency in scoring
        """
        self.max_entry_size_mb = max_entry_size_mb
        self.frequency_weight = frequency_weight
    
    def select_for_eviction(
        self,
        entries: List['CacheEntry'],
        required_space: int,
        available_space: int,
    ) -> List['CacheEntry']:
        """Select entries for eviction based on LRU with frequency consideration."""
        if not entries:
            return []
        
        candidates = []
        now = datetime.now()
        
        # Calculate access frequency statistics
        access_counts = [entry.access_count for entry in entries]
        max_access_count = max(access_counts) if access_counts else 1
        
        for entry in entries:
            candidate = EvictionCandidate.from_cache_entry(entry)
            
            # Calculate score based on recency and frequency
            time_since_access = (now - entry.accessed_at).total_seconds()
            
            # Normalize access count (0-1)
            frequency_score = entry.access_count / max_access_count
            
            # Combine recency and frequency (higher score = higher priority for eviction)
            recency_score = time_since_access / 3600  # Normalize to hours
            candidate.score = recency_score - (self.frequency_weight * frequency_score)
            
            candidates.append(candidate)
        
        # Sort by score (highest first = highest priority for eviction)
        candidates.sort(key=lambda c: c.score, reverse=True)
        
        # Select entries to evict
        evicted = []
        freed_space = 0
        
        for candidate in candidates:
            if freed_space >= required_space:
                break
            
            # Find corresponding entry
            entry = next(e for e in entries if e.key == candidate.key)
            evicted.append(entry)
            freed_space += entry.size_bytes
        
        return evicted
    
    def should_cache(self, content_size: int, metadata: Dict[str, Any]) -> bool:
        """Determine if content should be cached based on size."""
        max_size_bytes = self.max_entry_size_mb * 1024 * 1024
        return content_size <= max_size_bytes


class SizeBasedStrategy(CacheStrategy):
    """
    Size-based cache strategy.
    
    Prioritizes eviction of large entries and considers
    size-to-value ratio for caching decisions.
    """
    
    def __init__(self, max_entry_size_mb: int = 10, size_penalty_factor: float = 2.0):
        """
        Initialize size-based strategy.
        
        Args:
            max_entry_size_mb: Maximum size for individual entries
            size_penalty_factor: Penalty factor for large entries
        """
        self.max_entry_size_mb = max_entry_size_mb
        self.size_penalty_factor = size_penalty_factor
    
    def select_for_eviction(
        self,
        entries: List['CacheEntry'],
        required_space: int,
        available_space: int,
    ) -> List['CacheEntry']:
        """Select entries for eviction based on size and access patterns."""
        if not entries:
            return []
        
        candidates = []
        now = datetime.now()
        
        # Calculate size statistics
        sizes = [entry.size_bytes for entry in entries]
        max_size = max(sizes) if sizes else 1
        
        for entry in entries:
            candidate = EvictionCandidate.from_cache_entry(entry)
            
            # Calculate score based on size and access patterns
            size_ratio = entry.size_bytes / max_size
            
            # Time since last access
            time_since_access = (now - entry.accessed_at).total_seconds()
            recency_score = time_since_access / 3600  # Normalize to hours
            
            # Access frequency (lower is worse)
            frequency_score = 1.0 / (entry.access_count + 1)
            
            # Combine factors (higher score = higher priority for eviction)
            candidate.score = (
                size_ratio * self.size_penalty_factor +
                recency_score +
                frequency_score
            )
            
            candidates.append(candidate)
        
        # Sort by score (highest first = highest priority for eviction)
        candidates.sort(key=lambda c: c.score, reverse=True)
        
        # Select entries to evict
        evicted = []
        freed_space = 0
        
        for candidate in candidates:
            if freed_space >= required_space:
                break
            
            # Find corresponding entry
            entry = next(e for e in entries if e.key == candidate.key)
            evicted.append(entry)
            freed_space += entry.size_bytes
        
        return evicted
    
    def should_cache(self, content_size: int, metadata: Dict[str, Any]) -> bool:
        """Determine if content should be cached based on size and value."""
        max_size_bytes = self.max_entry_size_mb * 1024 * 1024
        
        if content_size > max_size_bytes:
            return False
        
        # Consider caching value based on content type
        content_type = metadata.get('content_type', '')
        
        # Prefer caching smaller, frequently accessed content
        if content_type in ['svg', 'png'] and content_size < 100 * 1024:  # < 100KB
            return True
        
        # Be more selective for larger content
        if content_size > 1024 * 1024:  # > 1MB
            return metadata.get('access_frequency', 0) > 5
        
        return True


class SmartStrategy(CacheStrategy):
    """
    Intelligent cache strategy that adapts based on usage patterns.
    
    Combines multiple factors including access patterns, content type,
    size, and performance metrics to make optimal caching decisions.
    """
    
    def __init__(
        self,
        max_entry_size_mb: int = 10,
        learning_window_hours: int = 24,
        adaptation_threshold: float = 0.1,
    ):
        """
        Initialize smart strategy.
        
        Args:
            max_entry_size_mb: Maximum size for individual entries
            learning_window_hours: Window for learning usage patterns
            adaptation_threshold: Threshold for strategy adaptation
        """
        self.max_entry_size_mb = max_entry_size_mb
        self.learning_window = timedelta(hours=learning_window_hours)
        self.adaptation_threshold = adaptation_threshold
        
        # Learning data
        self._access_patterns: Dict[str, List[datetime]] = {}
        self._performance_history: List[Dict[str, Any]] = []
    
    def select_for_eviction(
        self,
        entries: List['CacheEntry'],
        required_space: int,
        available_space: int,
    ) -> List['CacheEntry']:
        """Select entries using intelligent scoring."""
        if not entries:
            return []
        
        candidates = []
        now = datetime.now()
        
        for entry in entries:
            candidate = EvictionCandidate.from_cache_entry(entry)
            candidate.score = self._calculate_smart_score(entry, now)
            candidates.append(candidate)
        
        # Sort by score (highest first = highest priority for eviction)
        candidates.sort(key=lambda c: c.score, reverse=True)
        
        # Select entries to evict
        evicted = []
        freed_space = 0
        
        for candidate in candidates:
            if freed_space >= required_space:
                break
            
            # Find corresponding entry
            entry = next(e for e in entries if e.key == candidate.key)
            evicted.append(entry)
            freed_space += entry.size_bytes
        
        return evicted
    
    def should_cache(self, content_size: int, metadata: Dict[str, Any]) -> bool:
        """Intelligent caching decision based on multiple factors."""
        max_size_bytes = self.max_entry_size_mb * 1024 * 1024
        
        if content_size > max_size_bytes:
            return False
        
        # Extract metadata
        content_type = metadata.get('content_type', '')
        diagram_type = metadata.get('diagram_type', '')
        complexity = metadata.get('complexity', 'medium')
        
        # Base score
        score = 0.5
        
        # Content type factors
        if content_type in ['svg', 'png']:
            score += 0.2
        elif content_type == 'pdf':
            score += 0.1
        
        # Diagram type factors
        if diagram_type in ['flowchart', 'sequence']:
            score += 0.1  # Common types
        
        # Complexity factors
        if complexity == 'high':
            score += 0.2  # Cache complex diagrams
        elif complexity == 'low':
            score -= 0.1  # Less benefit for simple diagrams
        
        # Size factors
        if content_size < 50 * 1024:  # < 50KB
            score += 0.1
        elif content_size > 1024 * 1024:  # > 1MB
            score -= 0.2
        
        # Historical performance
        if self._performance_history:
            recent_hit_rate = self._calculate_recent_hit_rate()
            if recent_hit_rate < 0.5:
                score += 0.1  # Be more aggressive when hit rate is low
        
        return score > 0.5
    
    def _calculate_smart_score(self, entry: 'CacheEntry', now: datetime) -> float:
        """Calculate intelligent eviction score."""
        score = 0.0
        
        # Time-based factors
        age_hours = (now - entry.created_at).total_seconds() / 3600
        time_since_access_hours = (now - entry.accessed_at).total_seconds() / 3600
        
        # Age penalty (older entries more likely to be evicted)
        score += age_hours * 0.1
        
        # Recency penalty (less recently accessed more likely to be evicted)
        score += time_since_access_hours * 0.2
        
        # Access frequency bonus (frequently accessed less likely to be evicted)
        access_frequency = entry.access_count / max(age_hours, 1)
        score -= access_frequency * 0.3
        
        # Size penalty (larger entries more likely to be evicted)
        size_mb = entry.size_bytes / (1024 * 1024)
        score += size_mb * 0.1
        
        # Content type factors
        if 'svg' in entry.key:
            score -= 0.1  # Prefer keeping SVG
        elif 'png' in entry.key:
            score += 0.05  # PNG less preferred
        
        # Expiration factor
        if entry.is_expired():
            score += 10.0  # Expired entries highest priority for eviction
        
        return score
    
    def _calculate_recent_hit_rate(self) -> float:
        """Calculate recent cache hit rate."""
        if not self._performance_history:
            return 0.5
        
        recent_window = datetime.now() - timedelta(hours=1)
        recent_data = [
            p for p in self._performance_history
            if datetime.fromisoformat(p['timestamp']) >= recent_window
        ]
        
        if not recent_data:
            return 0.5
        
        hits = sum(1 for p in recent_data if p.get('cache_hit', False))
        return hits / len(recent_data)
    
    def record_access(self, key: str, timestamp: Optional[datetime] = None) -> None:
        """Record access for learning."""
        if timestamp is None:
            timestamp = datetime.now()
        
        if key not in self._access_patterns:
            self._access_patterns[key] = []
        
        self._access_patterns[key].append(timestamp)
        
        # Clean old data
        cutoff = timestamp - self.learning_window
        self._access_patterns[key] = [
            t for t in self._access_patterns[key] if t >= cutoff
        ]
    
    def record_performance(self, performance_data: Dict[str, Any]) -> None:
        """Record performance data for learning."""
        performance_data['timestamp'] = datetime.now().isoformat()
        self._performance_history.append(performance_data)
        
        # Keep only recent data
        cutoff = datetime.now() - self.learning_window
        self._performance_history = [
            p for p in self._performance_history
            if datetime.fromisoformat(p['timestamp']) >= cutoff
        ]
