"""
Performance monitoring and metrics for the cache system.

This module provides comprehensive performance monitoring capabilities
including metrics collection, analysis, and reporting.
"""

import time
import threading
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
import statistics


@dataclass
class RenderingMetrics:
    """Metrics for diagram rendering operations."""
    
    operation_type: str
    diagram_type: str
    format: str
    duration_seconds: float
    content_size_bytes: int
    cache_hit: bool
    timestamp: datetime
    error: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'operation_type': self.operation_type,
            'diagram_type': self.diagram_type,
            'format': self.format,
            'duration_seconds': self.duration_seconds,
            'content_size_bytes': self.content_size_bytes,
            'cache_hit': self.cache_hit,
            'timestamp': self.timestamp.isoformat(),
            'error': self.error,
        }


@dataclass
class CacheMetrics:
    """Metrics for cache operations."""
    
    operation: str  # get, put, delete, clear
    key: str
    duration_seconds: float
    success: bool
    size_bytes: Optional[int] = None
    timestamp: datetime = field(default_factory=datetime.now)
    error: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'operation': self.operation,
            'key': self.key,
            'duration_seconds': self.duration_seconds,
            'success': self.success,
            'size_bytes': self.size_bytes,
            'timestamp': self.timestamp.isoformat(),
            'error': self.error,
        }


@dataclass
class PerformanceReport:
    """Comprehensive performance report."""
    
    report_period: timedelta
    total_operations: int
    cache_hit_rate: float
    average_render_time: float
    average_cache_time: float
    throughput_ops_per_second: float
    error_rate: float
    size_distribution: Dict[str, int]
    format_distribution: Dict[str, int]
    diagram_type_distribution: Dict[str, int]
    performance_trends: Dict[str, List[float]]
    recommendations: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'report_period_seconds': self.report_period.total_seconds(),
            'total_operations': self.total_operations,
            'cache_hit_rate': self.cache_hit_rate,
            'average_render_time': self.average_render_time,
            'average_cache_time': self.average_cache_time,
            'throughput_ops_per_second': self.throughput_ops_per_second,
            'error_rate': self.error_rate,
            'size_distribution': self.size_distribution,
            'format_distribution': self.format_distribution,
            'diagram_type_distribution': self.diagram_type_distribution,
            'performance_trends': self.performance_trends,
            'recommendations': self.recommendations,
        }


class PerformanceMonitor:
    """
    Performance monitoring system for cache and rendering operations.
    
    Collects, analyzes, and reports on performance metrics to help
    optimize cache configuration and identify bottlenecks.
    """
    
    def __init__(
        self,
        max_metrics: int = 10000,
        enable_detailed_tracking: bool = True,
        report_interval_minutes: int = 60,
    ):
        """
        Initialize performance monitor.
        
        Args:
            max_metrics: Maximum number of metrics to keep in memory
            enable_detailed_tracking: Whether to track detailed metrics
            report_interval_minutes: Interval for automatic reports
        """
        self.max_metrics = max_metrics
        self.enable_detailed_tracking = enable_detailed_tracking
        self.report_interval = timedelta(minutes=report_interval_minutes)
        
        self._rendering_metrics: deque = deque(maxlen=max_metrics)
        self._cache_metrics: deque = deque(maxlen=max_metrics)
        self._lock = threading.RLock()
        
        # Aggregated statistics
        self._stats = {
            'total_renders': 0,
            'total_cache_hits': 0,
            'total_cache_misses': 0,
            'total_errors': 0,
            'total_render_time': 0.0,
            'total_cache_time': 0.0,
        }
        
        # Performance tracking
        self._start_time = datetime.now()
        self._last_report_time = self._start_time
    
    def record_render_operation(
        self,
        operation_type: str,
        diagram_type: str,
        format: str,
        duration: float,
        content_size: int,
        cache_hit: bool,
        error: Optional[str] = None,
    ) -> None:
        """
        Record a rendering operation.
        
        Args:
            operation_type: Type of operation (render, save, etc.)
            diagram_type: Type of diagram
            format: Output format
            duration: Operation duration in seconds
            content_size: Size of generated content in bytes
            cache_hit: Whether this was a cache hit
            error: Error message if operation failed
        """
        with self._lock:
            metric = RenderingMetrics(
                operation_type=operation_type,
                diagram_type=diagram_type,
                format=format,
                duration_seconds=duration,
                content_size_bytes=content_size,
                cache_hit=cache_hit,
                timestamp=datetime.now(),
                error=error,
            )
            
            if self.enable_detailed_tracking:
                self._rendering_metrics.append(metric)
            
            # Update aggregated stats
            self._stats['total_renders'] += 1
            self._stats['total_render_time'] += duration
            
            if cache_hit:
                self._stats['total_cache_hits'] += 1
            else:
                self._stats['total_cache_misses'] += 1
            
            if error:
                self._stats['total_errors'] += 1
    
    def record_cache_hit(self, key: str, duration: float) -> None:
        """Record a cache hit operation."""
        self._record_cache_operation('get', key, duration, True)
    
    def record_cache_miss(self, key: str) -> None:
        """Record a cache miss operation."""
        self._record_cache_operation('get', key, 0.0, True)
    
    def record_cache_put(self, key: str, size_bytes: int, duration: float) -> None:
        """Record a cache put operation."""
        self._record_cache_operation('put', key, duration, True, size_bytes)
    
    def record_cache_error(self, key: str, error: str) -> None:
        """Record a cache error."""
        self._record_cache_operation('error', key, 0.0, False, error=error)
    
    def _record_cache_operation(
        self,
        operation: str,
        key: str,
        duration: float,
        success: bool,
        size_bytes: Optional[int] = None,
        error: Optional[str] = None,
    ) -> None:
        """Record a cache operation."""
        with self._lock:
            metric = CacheMetrics(
                operation=operation,
                key=key,
                duration_seconds=duration,
                success=success,
                size_bytes=size_bytes,
                error=error,
            )
            
            if self.enable_detailed_tracking:
                self._cache_metrics.append(metric)
            
            # Update aggregated stats
            self._stats['total_cache_time'] += duration
            
            if not success:
                self._stats['total_errors'] += 1
    
    def get_current_stats(self) -> Dict[str, Any]:
        """Get current performance statistics."""
        with self._lock:
            total_cache_ops = self._stats['total_cache_hits'] + self._stats['total_cache_misses']
            hit_rate = self._stats['total_cache_hits'] / total_cache_ops if total_cache_ops > 0 else 0
            
            avg_render_time = (
                self._stats['total_render_time'] / self._stats['total_renders']
                if self._stats['total_renders'] > 0 else 0
            )
            
            uptime = datetime.now() - self._start_time
            throughput = self._stats['total_renders'] / uptime.total_seconds() if uptime.total_seconds() > 0 else 0
            
            return {
                'uptime_seconds': uptime.total_seconds(),
                'total_renders': self._stats['total_renders'],
                'cache_hit_rate': hit_rate,
                'average_render_time': avg_render_time,
                'throughput_ops_per_second': throughput,
                'total_errors': self._stats['total_errors'],
                'error_rate': self._stats['total_errors'] / self._stats['total_renders'] if self._stats['total_renders'] > 0 else 0,
            }
    
    def get_report(self, period: Optional[timedelta] = None) -> PerformanceReport:
        """
        Generate comprehensive performance report.
        
        Args:
            period: Time period for report (default: since last report)
            
        Returns:
            Performance report
        """
        with self._lock:
            if period is None:
                period = datetime.now() - self._last_report_time
                self._last_report_time = datetime.now()
            
            cutoff_time = datetime.now() - period
            
            # Filter metrics by time period
            recent_renders = [
                m for m in self._rendering_metrics
                if m.timestamp >= cutoff_time
            ]
            
            recent_cache_ops = [
                m for m in self._cache_metrics
                if m.timestamp >= cutoff_time
            ]
            
            # Calculate metrics
            total_operations = len(recent_renders)
            
            if total_operations == 0:
                return PerformanceReport(
                    report_period=period,
                    total_operations=0,
                    cache_hit_rate=0.0,
                    average_render_time=0.0,
                    average_cache_time=0.0,
                    throughput_ops_per_second=0.0,
                    error_rate=0.0,
                    size_distribution={},
                    format_distribution={},
                    diagram_type_distribution={},
                    performance_trends={},
                    recommendations=[],
                )
            
            # Cache hit rate
            cache_hits = sum(1 for m in recent_renders if m.cache_hit)
            cache_hit_rate = cache_hits / total_operations
            
            # Average times
            render_times = [m.duration_seconds for m in recent_renders]
            average_render_time = statistics.mean(render_times)
            
            cache_times = [m.duration_seconds for m in recent_cache_ops if m.success]
            average_cache_time = statistics.mean(cache_times) if cache_times else 0.0
            
            # Throughput
            throughput = total_operations / period.total_seconds()
            
            # Error rate
            errors = sum(1 for m in recent_renders if m.error is not None)
            error_rate = errors / total_operations
            
            # Distributions
            size_distribution = self._calculate_size_distribution(recent_renders)
            format_distribution = self._calculate_format_distribution(recent_renders)
            diagram_type_distribution = self._calculate_diagram_type_distribution(recent_renders)
            
            # Performance trends
            performance_trends = self._calculate_performance_trends(recent_renders)
            
            # Recommendations
            recommendations = self._generate_recommendations(
                cache_hit_rate, average_render_time, error_rate, recent_renders
            )
            
            return PerformanceReport(
                report_period=period,
                total_operations=total_operations,
                cache_hit_rate=cache_hit_rate,
                average_render_time=average_render_time,
                average_cache_time=average_cache_time,
                throughput_ops_per_second=throughput,
                error_rate=error_rate,
                size_distribution=size_distribution,
                format_distribution=format_distribution,
                diagram_type_distribution=diagram_type_distribution,
                performance_trends=performance_trends,
                recommendations=recommendations,
            )
    
    def _calculate_size_distribution(self, metrics: List[RenderingMetrics]) -> Dict[str, int]:
        """Calculate content size distribution."""
        distribution = defaultdict(int)
        
        for metric in metrics:
            size_kb = metric.content_size_bytes // 1024
            
            if size_kb < 1:
                bucket = "< 1KB"
            elif size_kb < 10:
                bucket = "1-10KB"
            elif size_kb < 100:
                bucket = "10-100KB"
            elif size_kb < 1000:
                bucket = "100KB-1MB"
            else:
                bucket = "> 1MB"
            
            distribution[bucket] += 1
        
        return dict(distribution)
    
    def _calculate_format_distribution(self, metrics: List[RenderingMetrics]) -> Dict[str, int]:
        """Calculate format distribution."""
        distribution = defaultdict(int)
        
        for metric in metrics:
            distribution[metric.format] += 1
        
        return dict(distribution)
    
    def _calculate_diagram_type_distribution(self, metrics: List[RenderingMetrics]) -> Dict[str, int]:
        """Calculate diagram type distribution."""
        distribution = defaultdict(int)
        
        for metric in metrics:
            distribution[metric.diagram_type] += 1
        
        return dict(distribution)
    
    def _calculate_performance_trends(self, metrics: List[RenderingMetrics]) -> Dict[str, List[float]]:
        """Calculate performance trends over time."""
        if len(metrics) < 10:
            return {}
        
        # Sort by timestamp
        sorted_metrics = sorted(metrics, key=lambda m: m.timestamp)
        
        # Calculate moving averages
        window_size = max(10, len(sorted_metrics) // 10)
        trends = {}
        
        # Render time trend
        render_times = []
        for i in range(len(sorted_metrics) - window_size + 1):
            window = sorted_metrics[i:i + window_size]
            avg_time = statistics.mean([m.duration_seconds for m in window])
            render_times.append(avg_time)
        
        trends['render_time'] = render_times
        
        # Cache hit rate trend
        hit_rates = []
        for i in range(len(sorted_metrics) - window_size + 1):
            window = sorted_metrics[i:i + window_size]
            hits = sum(1 for m in window if m.cache_hit)
            hit_rate = hits / len(window)
            hit_rates.append(hit_rate)
        
        trends['cache_hit_rate'] = hit_rates
        
        return trends
    
    def _generate_recommendations(
        self,
        cache_hit_rate: float,
        average_render_time: float,
        error_rate: float,
        metrics: List[RenderingMetrics],
    ) -> List[str]:
        """Generate performance recommendations."""
        recommendations = []
        
        # Cache hit rate recommendations
        if cache_hit_rate < 0.5:
            recommendations.append(
                "Low cache hit rate detected. Consider increasing cache size or TTL."
            )
        elif cache_hit_rate > 0.9:
            recommendations.append(
                "Excellent cache hit rate. Cache configuration is optimal."
            )
        
        # Render time recommendations
        if average_render_time > 5.0:
            recommendations.append(
                "High average render time. Consider optimizing diagram complexity or using faster rendering backend."
            )
        
        # Error rate recommendations
        if error_rate > 0.1:
            recommendations.append(
                "High error rate detected. Check diagram validation and rendering configuration."
            )
        
        # Format-specific recommendations
        format_times = defaultdict(list)
        for metric in metrics:
            format_times[metric.format].append(metric.duration_seconds)
        
        for format, times in format_times.items():
            if len(times) > 5:
                avg_time = statistics.mean(times)
                if avg_time > 3.0:
                    recommendations.append(
                        f"Format '{format}' has high render times. Consider caching or optimization."
                    )
        
        return recommendations
