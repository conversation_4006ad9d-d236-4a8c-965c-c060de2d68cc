"""
Advanced caching and performance optimization for Mermaid Render.

This package provides intelligent caching capabilities with multiple storage
backends, performance monitoring, and optimization features to significantly
improve rendering performance for repeated operations.

Features:
- Multiple cache storage backends (memory, file, Redis)
- Intelligent cache invalidation strategies
- Performance monitoring and metrics
- Cache warming and preloading
- Compression and optimization
- Distributed caching support
- Cache analytics and reporting

Example:
    >>> from mermaid_render.cache import CacheManager, MemoryBackend
    >>> from mermaid_render import MermaidRenderer
    >>> 
    >>> # Setup caching
    >>> cache_manager = CacheManager(backend=MemoryBackend())
    >>> renderer = MermaidRenderer(cache_manager=cache_manager)
    >>> 
    >>> # First render (cache miss)
    >>> svg1 = renderer.render(diagram, format="svg")  # Slow
    >>> 
    >>> # Second render (cache hit)
    >>> svg2 = renderer.render(diagram, format="svg")  # Fast!
    >>> 
    >>> # View cache statistics
    >>> stats = cache_manager.get_statistics()
    >>> print(f"Hit rate: {stats['hit_rate']:.2%}")
"""

from .cache_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from .storage_backends import (
    MemoryBackend,
    FileBackend,
    RedisBackend,
    CompositeCacheBackend,
)
from .performance import (
    PerformanceMonitor,
    RenderingMetrics,
    CacheMetrics,
    PerformanceReport,
)
from .strategies import (
    CacheStrategy,
    TTLStrategy,
    LRUStrategy,
    SizeBasedStrategy,
    SmartStrategy,
)
from .optimization import (
    CacheOptimizer,
    CompressionOptimizer,
    PrefetchOptimizer,
    WarmupOptimizer,
)

# Convenience functions
from .utils import (
    create_cache_manager,
    warm_cache,
    clear_cache,
    get_cache_stats,
    optimize_cache,
    export_cache_data,
    import_cache_data,
)

__all__ = [
    # Core classes
    "CacheManager",
    "CacheKey",
    "CacheEntry",
    
    # Storage backends
    "MemoryBackend",
    "FileBackend", 
    "RedisBackend",
    "CompositeCacheBackend",
    
    # Performance monitoring
    "PerformanceMonitor",
    "RenderingMetrics",
    "CacheMetrics",
    "PerformanceReport",
    
    # Cache strategies
    "CacheStrategy",
    "TTLStrategy",
    "LRUStrategy",
    "SizeBasedStrategy",
    "SmartStrategy",
    
    # Optimization
    "CacheOptimizer",
    "CompressionOptimizer",
    "PrefetchOptimizer",
    "WarmupOptimizer",
    
    # Utilities
    "create_cache_manager",
    "warm_cache",
    "clear_cache",
    "get_cache_stats",
    "optimize_cache",
    "export_cache_data",
    "import_cache_data",
]
