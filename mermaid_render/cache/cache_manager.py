"""
Core cache management functionality.

This module provides the main cache management system with support for
multiple backends, intelligent invalidation, and performance optimization.
"""

import hashlib
import time
import json
from typing import Dict, Any, Optional, Union, List, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
import threading
from pathlib import Path

from ..exceptions import CacheError
from .storage_backends import CacheBackend, MemoryBackend
from .strategies import CacheStrategy, TTLStrategy
from .performance import PerformanceMonitor, RenderingMetrics


class CacheKeyType(Enum):
    """Types of cache keys for different content."""
    DIAGRAM = "diagram"
    TEMPLATE = "template"
    VALIDATION = "validation"
    THEME = "theme"
    EXPORT = "export"


@dataclass
class CacheKey:
    """
    Represents a cache key with metadata.
    
    Provides a structured way to create and manage cache keys
    with support for different content types and versioning.
    """
    
    content_hash: str
    key_type: CacheKeyType
    format: Optional[str] = None
    theme: Optional[str] = None
    options: Optional[Dict[str, Any]] = None
    version: str = "1.0"
    
    def __post_init__(self):
        if self.options is None:
            self.options = {}
    
    def to_string(self) -> str:
        """Convert cache key to string representation."""
        parts = [
            self.key_type.value,
            self.content_hash,
            self.version,
        ]
        
        if self.format:
            parts.append(f"fmt_{self.format}")
        
        if self.theme:
            parts.append(f"theme_{self.theme}")
        
        if self.options:
            # Sort options for consistent key generation
            options_str = json.dumps(self.options, sort_keys=True, separators=(',', ':'))
            options_hash = hashlib.md5(options_str.encode()).hexdigest()[:8]
            parts.append(f"opts_{options_hash}")
        
        return ":".join(parts)
    
    @classmethod
    def from_content(
        cls,
        content: str,
        key_type: CacheKeyType,
        format: Optional[str] = None,
        theme: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None,
        version: str = "1.0",
    ) -> 'CacheKey':
        """Create cache key from content."""
        content_hash = hashlib.sha256(content.encode()).hexdigest()[:16]
        
        return cls(
            content_hash=content_hash,
            key_type=key_type,
            format=format,
            theme=theme,
            options=options,
            version=version,
        )


@dataclass
class CacheEntry:
    """
    Represents a cached entry with metadata.
    
    Stores cached content along with metadata for cache management,
    invalidation, and performance tracking.
    """
    
    key: str
    content: Any
    content_type: str
    size_bytes: int
    created_at: datetime
    accessed_at: datetime
    access_count: int
    ttl_seconds: Optional[int] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.metadata is None:
            self.metadata = {}
    
    def is_expired(self) -> bool:
        """Check if cache entry is expired."""
        if self.ttl_seconds is None:
            return False
        
        expiry_time = self.created_at + timedelta(seconds=self.ttl_seconds)
        return datetime.now() > expiry_time
    
    def update_access(self) -> None:
        """Update access metadata."""
        self.accessed_at = datetime.now()
        self.access_count += 1
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        data['accessed_at'] = self.accessed_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CacheEntry':
        """Create from dictionary."""
        data = data.copy()
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        data['accessed_at'] = datetime.fromisoformat(data['accessed_at'])
        return cls(**data)


class CacheManager:
    """
    Main cache management system.
    
    Provides high-level caching functionality with support for multiple
    backends, strategies, and performance monitoring.
    """
    
    def __init__(
        self,
        backend: Optional[CacheBackend] = None,
        strategy: Optional[CacheStrategy] = None,
        performance_monitor: Optional[PerformanceMonitor] = None,
        max_size_mb: int = 100,
        default_ttl: int = 3600,
        enable_compression: bool = True,
        enable_metrics: bool = True,
    ):
        """
        Initialize cache manager.
        
        Args:
            backend: Cache storage backend
            strategy: Cache eviction strategy
            performance_monitor: Performance monitoring instance
            max_size_mb: Maximum cache size in MB
            default_ttl: Default TTL in seconds
            enable_compression: Whether to enable compression
            enable_metrics: Whether to collect metrics
        """
        self.backend = backend or MemoryBackend()
        self.strategy = strategy or TTLStrategy(default_ttl=default_ttl)
        self.performance_monitor = performance_monitor or PerformanceMonitor()
        
        self.max_size_mb = max_size_mb
        self.default_ttl = default_ttl
        self.enable_compression = enable_compression
        self.enable_metrics = enable_metrics
        
        self._lock = threading.RLock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'size_bytes': 0,
            'entry_count': 0,
        }
    
    def get(self, key: Union[str, CacheKey]) -> Optional[Any]:
        """
        Get item from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached content or None if not found
        """
        start_time = time.time()
        
        with self._lock:
            key_str = key.to_string() if isinstance(key, CacheKey) else key
            
            try:
                entry = self.backend.get(key_str)
                
                if entry is None:
                    self._stats['misses'] += 1
                    if self.enable_metrics:
                        self.performance_monitor.record_cache_miss(key_str)
                    return None
                
                # Check expiration
                if entry.is_expired():
                    self.backend.delete(key_str)
                    self._stats['misses'] += 1
                    if self.enable_metrics:
                        self.performance_monitor.record_cache_miss(key_str)
                    return None
                
                # Update access metadata
                entry.update_access()
                self.backend.put(key_str, entry)
                
                self._stats['hits'] += 1
                
                if self.enable_metrics:
                    duration = time.time() - start_time
                    self.performance_monitor.record_cache_hit(key_str, duration)
                
                return entry.content
                
            except Exception as e:
                if self.enable_metrics:
                    self.performance_monitor.record_cache_error(key_str, str(e))
                raise CacheError(f"Cache get failed: {str(e)}") from e
    
    def put(
        self,
        key: Union[str, CacheKey],
        content: Any,
        ttl: Optional[int] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Put item in cache.
        
        Args:
            key: Cache key
            content: Content to cache
            ttl: Time to live in seconds
            tags: Optional tags for categorization
            metadata: Optional metadata
        """
        start_time = time.time()
        
        with self._lock:
            key_str = key.to_string() if isinstance(key, CacheKey) else key
            
            try:
                # Calculate content size
                if isinstance(content, str):
                    size_bytes = len(content.encode('utf-8'))
                    content_type = "text"
                elif isinstance(content, bytes):
                    size_bytes = len(content)
                    content_type = "binary"
                else:
                    # Serialize to JSON for size calculation
                    serialized = json.dumps(content, default=str)
                    size_bytes = len(serialized.encode('utf-8'))
                    content_type = "object"
                
                # Check size limits
                max_size_bytes = self.max_size_mb * 1024 * 1024
                if size_bytes > max_size_bytes // 10:  # Single item can't be more than 10% of cache
                    raise CacheError(f"Content too large: {size_bytes} bytes")
                
                # Create cache entry
                now = datetime.now()
                entry = CacheEntry(
                    key=key_str,
                    content=content,
                    content_type=content_type,
                    size_bytes=size_bytes,
                    created_at=now,
                    accessed_at=now,
                    access_count=0,
                    ttl_seconds=ttl or self.default_ttl,
                    tags=tags or [],
                    metadata=metadata or {},
                )
                
                # Apply cache strategy (eviction if needed)
                self._apply_strategy_before_put(entry)
                
                # Store in backend
                self.backend.put(key_str, entry)
                
                # Update stats
                self._stats['size_bytes'] += size_bytes
                self._stats['entry_count'] += 1
                
                if self.enable_metrics:
                    duration = time.time() - start_time
                    self.performance_monitor.record_cache_put(key_str, size_bytes, duration)
                
            except Exception as e:
                if self.enable_metrics:
                    self.performance_monitor.record_cache_error(key_str, str(e))
                raise CacheError(f"Cache put failed: {str(e)}") from e
    
    def delete(self, key: Union[str, CacheKey]) -> bool:
        """
        Delete item from cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if item was deleted, False if not found
        """
        with self._lock:
            key_str = key.to_string() if isinstance(key, CacheKey) else key
            
            try:
                entry = self.backend.get(key_str)
                if entry:
                    self.backend.delete(key_str)
                    self._stats['size_bytes'] -= entry.size_bytes
                    self._stats['entry_count'] -= 1
                    return True
                return False
                
            except Exception as e:
                raise CacheError(f"Cache delete failed: {str(e)}") from e
    
    def clear(self, tags: Optional[List[str]] = None) -> int:
        """
        Clear cache entries.
        
        Args:
            tags: Optional tags to filter entries to clear
            
        Returns:
            Number of entries cleared
        """
        with self._lock:
            try:
                if tags:
                    # Clear entries with specific tags
                    cleared_count = 0
                    for key in list(self.backend.keys()):
                        entry = self.backend.get(key)
                        if entry and any(tag in entry.tags for tag in tags):
                            self.backend.delete(key)
                            self._stats['size_bytes'] -= entry.size_bytes
                            self._stats['entry_count'] -= 1
                            cleared_count += 1
                    return cleared_count
                else:
                    # Clear all entries
                    count = self._stats['entry_count']
                    self.backend.clear()
                    self._stats['size_bytes'] = 0
                    self._stats['entry_count'] = 0
                    return count
                    
            except Exception as e:
                raise CacheError(f"Cache clear failed: {str(e)}") from e
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0
            
            return {
                'hits': self._stats['hits'],
                'misses': self._stats['misses'],
                'hit_rate': hit_rate,
                'evictions': self._stats['evictions'],
                'size_bytes': self._stats['size_bytes'],
                'size_mb': self._stats['size_bytes'] / (1024 * 1024),
                'entry_count': self._stats['entry_count'],
                'max_size_mb': self.max_size_mb,
                'utilization': self._stats['size_bytes'] / (self.max_size_mb * 1024 * 1024),
            }
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get performance monitoring report."""
        if self.enable_metrics:
            return self.performance_monitor.get_report()
        return {}
    
    def _apply_strategy_before_put(self, new_entry: CacheEntry) -> None:
        """Apply cache strategy before putting new entry."""
        # Check if we need to evict entries
        max_size_bytes = self.max_size_mb * 1024 * 1024
        
        if self._stats['size_bytes'] + new_entry.size_bytes > max_size_bytes:
            # Need to evict entries
            entries_to_evict = self.strategy.select_for_eviction(
                self.backend.get_all_entries(),
                new_entry.size_bytes,
                max_size_bytes - self._stats['size_bytes']
            )
            
            for entry in entries_to_evict:
                self.backend.delete(entry.key)
                self._stats['size_bytes'] -= entry.size_bytes
                self._stats['entry_count'] -= 1
                self._stats['evictions'] += 1
