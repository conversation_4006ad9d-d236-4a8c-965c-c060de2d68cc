"""
Utility functions for the cache system.

This module provides convenience functions for cache management,
configuration, and common operations.
"""

import json
import pickle
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from datetime import datetime

from .cache_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>acheKeyType
from .storage_backends import <PERSON><PERSON>ackend, FileBackend, RedisBackend, CompositeCacheBackend
from .strategies import TTLStrategy, LRUStrategy, SizeBasedStrategy, SmartStrategy
from .performance import PerformanceMonitor
from .optimization import CompressionOptimizer, PrefetchOptimizer, WarmupOptimizer, AdaptiveOptimizer
from ..exceptions import CacheError


def create_cache_manager(
    backend_type: str = 'memory',
    strategy_type: str = 'ttl',
    enable_performance_monitoring: bool = True,
    **config
) -> CacheManager:
    """
    Create a cache manager with specified configuration.
    
    Args:
        backend_type: Type of storage backend (memory, file, redis, composite)
        strategy_type: Type of cache strategy (ttl, lru, size, smart)
        enable_performance_monitoring: Whether to enable performance monitoring
        **config: Additional configuration options
        
    Returns:
        Configured cache manager
        
    Example:
        >>> # Create memory-based cache
        >>> cache = create_cache_manager('memory', max_entries=1000)
        >>> 
        >>> # Create file-based cache with LRU strategy
        >>> cache = create_cache_manager(
        ...     'file',
        ...     'lru',
        ...     cache_dir='/tmp/mermaid_cache',
        ...     max_size_mb=500
        ... )
        >>> 
        >>> # Create Redis cache with smart strategy
        >>> cache = create_cache_manager(
        ...     'redis',
        ...     'smart',
        ...     host='localhost',
        ...     port=6379
        ... )
    """
    # Create storage backend
    backend = _create_backend(backend_type, config)
    
    # Create cache strategy
    strategy = _create_strategy(strategy_type, config)
    
    # Create performance monitor
    performance_monitor = None
    if enable_performance_monitoring:
        performance_monitor = PerformanceMonitor(
            max_metrics=config.get('max_metrics', 10000),
            enable_detailed_tracking=config.get('enable_detailed_tracking', True),
        )
    
    # Create cache manager
    return CacheManager(
        backend=backend,
        strategy=strategy,
        performance_monitor=performance_monitor,
        max_size_mb=config.get('max_size_mb', 100),
        default_ttl=config.get('default_ttl', 3600),
        enable_compression=config.get('enable_compression', True),
        enable_metrics=config.get('enable_metrics', True),
    )


def _create_backend(backend_type: str, config: Dict[str, Any]):
    """Create storage backend based on type."""
    if backend_type == 'memory':
        return MemoryBackend(
            max_entries=config.get('max_entries', 10000)
        )
    
    elif backend_type == 'file':
        cache_dir = config.get('cache_dir', Path.home() / '.mermaid_render_cache')
        return FileBackend(
            cache_dir=Path(cache_dir),
            max_size_mb=config.get('max_size_mb', 1000)
        )
    
    elif backend_type == 'redis':
        return RedisBackend(
            host=config.get('host', 'localhost'),
            port=config.get('port', 6379),
            db=config.get('db', 0),
            password=config.get('password'),
            key_prefix=config.get('key_prefix', 'mermaid_cache:'),
            serialization=config.get('serialization', 'pickle'),
        )
    
    elif backend_type == 'composite':
        # Create multi-level cache
        backends = []
        
        # Add memory backend as L1 cache
        backends.append(MemoryBackend(max_entries=config.get('l1_max_entries', 1000)))
        
        # Add file backend as L2 cache
        if config.get('enable_file_cache', True):
            cache_dir = config.get('cache_dir', Path.home() / '.mermaid_render_cache')
            backends.append(FileBackend(cache_dir=Path(cache_dir)))
        
        # Add Redis backend as L3 cache
        if config.get('enable_redis_cache', False):
            backends.append(RedisBackend(
                host=config.get('redis_host', 'localhost'),
                port=config.get('redis_port', 6379),
            ))
        
        return CompositeCacheBackend(backends)
    
    else:
        raise CacheError(f"Unsupported backend type: {backend_type}")


def _create_strategy(strategy_type: str, config: Dict[str, Any]):
    """Create cache strategy based on type."""
    if strategy_type == 'ttl':
        return TTLStrategy(
            default_ttl=config.get('default_ttl', 3600),
            max_entry_size_mb=config.get('max_entry_size_mb', 10),
        )
    
    elif strategy_type == 'lru':
        return LRUStrategy(
            max_entry_size_mb=config.get('max_entry_size_mb', 10),
            frequency_weight=config.get('frequency_weight', 0.3),
        )
    
    elif strategy_type == 'size':
        return SizeBasedStrategy(
            max_entry_size_mb=config.get('max_entry_size_mb', 10),
            size_penalty_factor=config.get('size_penalty_factor', 2.0),
        )
    
    elif strategy_type == 'smart':
        return SmartStrategy(
            max_entry_size_mb=config.get('max_entry_size_mb', 10),
            learning_window_hours=config.get('learning_window_hours', 24),
            adaptation_threshold=config.get('adaptation_threshold', 0.1),
        )
    
    else:
        raise CacheError(f"Unsupported strategy type: {strategy_type}")


def warm_cache(
    cache_manager: CacheManager,
    content_list: List[Dict[str, Any]],
    max_workers: int = 4,
) -> Dict[str, Any]:
    """
    Warm up cache with predefined content.
    
    Args:
        cache_manager: Cache manager to warm up
        content_list: List of content to cache
        max_workers: Maximum number of worker threads
        
    Returns:
        Warmup statistics
        
    Example:
        >>> content = [
        ...     {
        ...         'key': 'flowchart_basic',
        ...         'content': 'flowchart TD\\n    A --> B',
        ...         'metadata': {'diagram_type': 'flowchart'}
        ...     }
        ... ]
        >>> stats = warm_cache(cache_manager, content)
    """
    warmed_count = 0
    failed_count = 0
    
    for item in content_list:
        try:
            key = item['key']
            content = item['content']
            metadata = item.get('metadata', {})
            ttl = item.get('ttl')
            tags = item.get('tags')
            
            cache_manager.put(key, content, ttl=ttl, tags=tags, metadata=metadata)
            warmed_count += 1
            
        except Exception:
            failed_count += 1
    
    return {
        'total_items': len(content_list),
        'warmed_count': warmed_count,
        'failed_count': failed_count,
        'success_rate': warmed_count / len(content_list) if content_list else 0,
    }


def clear_cache(
    cache_manager: CacheManager,
    tags: Optional[List[str]] = None,
    older_than: Optional[datetime] = None,
) -> Dict[str, Any]:
    """
    Clear cache entries with optional filtering.
    
    Args:
        cache_manager: Cache manager to clear
        tags: Optional tags to filter entries
        older_than: Optional datetime to clear entries older than
        
    Returns:
        Clear operation statistics
        
    Example:
        >>> # Clear all cache
        >>> stats = clear_cache(cache_manager)
        >>> 
        >>> # Clear entries with specific tags
        >>> stats = clear_cache(cache_manager, tags=['flowchart', 'temp'])
        >>> 
        >>> # Clear old entries
        >>> from datetime import datetime, timedelta
        >>> cutoff = datetime.now() - timedelta(days=7)
        >>> stats = clear_cache(cache_manager, older_than=cutoff)
    """
    if older_than:
        # Custom clearing logic for time-based filtering
        cleared_count = 0
        entries = cache_manager.backend.get_all_entries()
        
        for entry in entries:
            if entry.created_at < older_than:
                if tags is None or any(tag in entry.tags for tag in tags):
                    cache_manager.delete(entry.key)
                    cleared_count += 1
        
        return {
            'cleared_count': cleared_count,
            'filter_type': 'time_based',
            'cutoff_time': older_than.isoformat(),
        }
    else:
        # Use cache manager's clear method
        cleared_count = cache_manager.clear(tags=tags)
        
        return {
            'cleared_count': cleared_count,
            'filter_type': 'tags' if tags else 'all',
            'tags': tags,
        }


def get_cache_stats(cache_manager: CacheManager) -> Dict[str, Any]:
    """
    Get comprehensive cache statistics.
    
    Args:
        cache_manager: Cache manager to analyze
        
    Returns:
        Comprehensive statistics
        
    Example:
        >>> stats = get_cache_stats(cache_manager)
        >>> print(f"Hit rate: {stats['hit_rate']:.2%}")
        >>> print(f"Cache size: {stats['size_mb']:.1f} MB")
    """
    basic_stats = cache_manager.get_statistics()
    performance_report = cache_manager.get_performance_report()
    
    # Add additional analysis
    entries = cache_manager.backend.get_all_entries()
    
    # Content type distribution
    content_types = {}
    for entry in entries:
        content_type = entry.content_type
        content_types[content_type] = content_types.get(content_type, 0) + 1
    
    # Size distribution
    size_distribution = {
        'small': 0,    # < 10KB
        'medium': 0,   # 10KB - 100KB
        'large': 0,    # 100KB - 1MB
        'xlarge': 0,   # > 1MB
    }
    
    for entry in entries:
        size_kb = entry.size_bytes / 1024
        if size_kb < 10:
            size_distribution['small'] += 1
        elif size_kb < 100:
            size_distribution['medium'] += 1
        elif size_kb < 1024:
            size_distribution['large'] += 1
        else:
            size_distribution['xlarge'] += 1
    
    # Age distribution
    now = datetime.now()
    age_distribution = {
        'fresh': 0,    # < 1 hour
        'recent': 0,   # 1-24 hours
        'old': 0,      # 1-7 days
        'stale': 0,    # > 7 days
    }
    
    for entry in entries:
        age_hours = (now - entry.created_at).total_seconds() / 3600
        if age_hours < 1:
            age_distribution['fresh'] += 1
        elif age_hours < 24:
            age_distribution['recent'] += 1
        elif age_hours < 168:  # 7 days
            age_distribution['old'] += 1
        else:
            age_distribution['stale'] += 1
    
    return {
        **basic_stats,
        'performance': performance_report,
        'content_type_distribution': content_types,
        'size_distribution': size_distribution,
        'age_distribution': age_distribution,
        'total_entries': len(entries),
    }


def optimize_cache(
    cache_manager: CacheManager,
    optimization_type: str = 'adaptive',
    **options
) -> Dict[str, Any]:
    """
    Optimize cache performance.
    
    Args:
        cache_manager: Cache manager to optimize
        optimization_type: Type of optimization (compression, prefetch, warmup, adaptive)
        **options: Additional optimization options
        
    Returns:
        Optimization results
        
    Example:
        >>> # Run adaptive optimization
        >>> results = optimize_cache(cache_manager, 'adaptive')
        >>> 
        >>> # Run specific optimization
        >>> results = optimize_cache(cache_manager, 'compression')
    """
    if optimization_type == 'compression':
        optimizer = CompressionOptimizer(**options)
    elif optimization_type == 'prefetch':
        optimizer = PrefetchOptimizer(**options)
    elif optimization_type == 'warmup':
        optimizer = WarmupOptimizer(**options)
    elif optimization_type == 'adaptive':
        optimizer = AdaptiveOptimizer()
    else:
        raise CacheError(f"Unsupported optimization type: {optimization_type}")
    
    return optimizer.optimize(cache_manager)


def export_cache_data(
    cache_manager: CacheManager,
    output_path: Union[str, Path],
    format: str = 'json',
    include_content: bool = True,
) -> Dict[str, Any]:
    """
    Export cache data to file.
    
    Args:
        cache_manager: Cache manager to export from
        output_path: Output file path
        format: Export format (json, pickle)
        include_content: Whether to include actual content
        
    Returns:
        Export statistics
    """
    output_path = Path(output_path)
    entries = cache_manager.backend.get_all_entries()
    
    export_data = {
        'export_timestamp': datetime.now().isoformat(),
        'cache_statistics': cache_manager.get_statistics(),
        'entries': [],
    }
    
    for entry in entries:
        entry_data = {
            'key': entry.key,
            'content_type': entry.content_type,
            'size_bytes': entry.size_bytes,
            'created_at': entry.created_at.isoformat(),
            'accessed_at': entry.accessed_at.isoformat(),
            'access_count': entry.access_count,
            'ttl_seconds': entry.ttl_seconds,
            'tags': entry.tags,
            'metadata': entry.metadata,
        }
        
        if include_content:
            entry_data['content'] = entry.content
        
        export_data['entries'].append(entry_data)
    
    # Write to file
    if format == 'json':
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, default=str)
    elif format == 'pickle':
        with open(output_path, 'wb') as f:
            pickle.dump(export_data, f)
    else:
        raise CacheError(f"Unsupported export format: {format}")
    
    return {
        'exported_entries': len(entries),
        'output_file': str(output_path),
        'file_size_bytes': output_path.stat().st_size,
        'format': format,
        'include_content': include_content,
    }


def import_cache_data(
    cache_manager: CacheManager,
    input_path: Union[str, Path],
    format: str = 'json',
    overwrite_existing: bool = False,
) -> Dict[str, Any]:
    """
    Import cache data from file.
    
    Args:
        cache_manager: Cache manager to import to
        input_path: Input file path
        format: Import format (json, pickle)
        overwrite_existing: Whether to overwrite existing entries
        
    Returns:
        Import statistics
    """
    input_path = Path(input_path)
    
    if not input_path.exists():
        raise CacheError(f"Import file not found: {input_path}")
    
    # Load data
    if format == 'json':
        with open(input_path, 'r', encoding='utf-8') as f:
            import_data = json.load(f)
    elif format == 'pickle':
        with open(input_path, 'rb') as f:
            import_data = pickle.load(f)
    else:
        raise CacheError(f"Unsupported import format: {format}")
    
    # Import entries
    imported_count = 0
    skipped_count = 0
    failed_count = 0
    
    for entry_data in import_data.get('entries', []):
        try:
            key = entry_data['key']
            
            # Check if entry already exists
            if not overwrite_existing and cache_manager.get(key) is not None:
                skipped_count += 1
                continue
            
            # Import entry
            cache_manager.put(
                key=key,
                content=entry_data.get('content'),
                ttl=entry_data.get('ttl_seconds'),
                tags=entry_data.get('tags', []),
                metadata=entry_data.get('metadata', {}),
            )
            
            imported_count += 1
            
        except Exception:
            failed_count += 1
    
    return {
        'total_entries': len(import_data.get('entries', [])),
        'imported_count': imported_count,
        'skipped_count': skipped_count,
        'failed_count': failed_count,
        'success_rate': imported_count / len(import_data.get('entries', [])) if import_data.get('entries') else 0,
        'source_file': str(input_path),
        'format': format,
    }
