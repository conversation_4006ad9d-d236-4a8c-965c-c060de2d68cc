"""
Cache optimization and enhancement features.

This module provides various optimization techniques including compression,
prefetching, cache warming, and intelligent optimization strategies.
"""

import gzip
import zlib
import threading
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable, Set
from datetime import datetime, timedelta
from concurrent.futures import <PERSON>hr<PERSON><PERSON>oolExecutor, Future
import hashlib
import json

from ..exceptions import CacheError


class CacheOptimizer(ABC):
    """Abstract base class for cache optimizers."""
    
    @abstractmethod
    def optimize(self, cache_manager: 'CacheManager') -> Dict[str, Any]:
        """
        Optimize cache performance.
        
        Args:
            cache_manager: Cache manager to optimize
            
        Returns:
            Optimization results and statistics
        """
        pass


class CompressionOptimizer(CacheOptimizer):
    """
    Compression optimizer for reducing cache storage requirements.
    
    Automatically compresses cache entries based on content type
    and size to maximize cache efficiency.
    """
    
    def __init__(
        self,
        compression_threshold_bytes: int = 1024,
        compression_method: str = 'gzip',
        compression_level: int = 6,
    ):
        """
        Initialize compression optimizer.
        
        Args:
            compression_threshold_bytes: Minimum size for compression
            compression_method: Compression method (gzip, zlib)
            compression_level: Compression level (1-9)
        """
        self.compression_threshold = compression_threshold_bytes
        self.compression_method = compression_method
        self.compression_level = compression_level
        
        self._compression_stats = {
            'total_compressed': 0,
            'total_original_size': 0,
            'total_compressed_size': 0,
            'compression_ratio': 0.0,
        }
    
    def optimize(self, cache_manager: 'CacheManager') -> Dict[str, Any]:
        """Optimize cache by compressing suitable entries."""
        optimized_count = 0
        space_saved = 0
        
        # Get all cache entries
        entries = cache_manager.backend.get_all_entries()
        
        for entry in entries:
            if self._should_compress(entry):
                original_size = entry.size_bytes
                compressed_content = self._compress_content(entry.content)
                
                if compressed_content and len(compressed_content) < original_size:
                    # Update entry with compressed content
                    entry.content = compressed_content
                    entry.size_bytes = len(compressed_content)
                    entry.metadata['compressed'] = True
                    entry.metadata['compression_method'] = self.compression_method
                    entry.metadata['original_size'] = original_size
                    
                    # Store updated entry
                    cache_manager.backend.put(entry.key, entry)
                    
                    optimized_count += 1
                    space_saved += original_size - len(compressed_content)
                    
                    # Update stats
                    self._compression_stats['total_compressed'] += 1
                    self._compression_stats['total_original_size'] += original_size
                    self._compression_stats['total_compressed_size'] += len(compressed_content)
        
        # Calculate compression ratio
        if self._compression_stats['total_original_size'] > 0:
            self._compression_stats['compression_ratio'] = (
                self._compression_stats['total_compressed_size'] /
                self._compression_stats['total_original_size']
            )
        
        return {
            'optimized_entries': optimized_count,
            'space_saved_bytes': space_saved,
            'space_saved_mb': space_saved / (1024 * 1024),
            'compression_stats': self._compression_stats.copy(),
        }
    
    def _should_compress(self, entry: 'CacheEntry') -> bool:
        """Determine if entry should be compressed."""
        # Skip if already compressed
        if entry.metadata.get('compressed', False):
            return False
        
        # Check size threshold
        if entry.size_bytes < self.compression_threshold:
            return False
        
        # Check content type
        if entry.content_type == 'text':
            return True
        elif entry.content_type == 'binary':
            # Only compress if it's likely to benefit (e.g., SVG)
            return 'svg' in entry.key.lower()
        
        return False
    
    def _compress_content(self, content: Any) -> Optional[bytes]:
        """Compress content using specified method."""
        try:
            if isinstance(content, str):
                data = content.encode('utf-8')
            elif isinstance(content, bytes):
                data = content
            else:
                # Serialize object to JSON first
                data = json.dumps(content, default=str).encode('utf-8')
            
            if self.compression_method == 'gzip':
                return gzip.compress(data, compresslevel=self.compression_level)
            elif self.compression_method == 'zlib':
                return zlib.compress(data, level=self.compression_level)
            else:
                return None
                
        except Exception:
            return None
    
    def decompress_content(self, compressed_data: bytes, method: str) -> Any:
        """Decompress content."""
        try:
            if method == 'gzip':
                data = gzip.decompress(compressed_data)
            elif method == 'zlib':
                data = zlib.decompress(compressed_data)
            else:
                raise CacheError(f"Unsupported compression method: {method}")
            
            # Try to decode as UTF-8 text first
            try:
                return data.decode('utf-8')
            except UnicodeDecodeError:
                # Return as bytes if not valid UTF-8
                return data
                
        except Exception as e:
            raise CacheError(f"Decompression failed: {str(e)}") from e


class PrefetchOptimizer(CacheOptimizer):
    """
    Prefetch optimizer for proactive cache population.
    
    Analyzes access patterns and prefetches likely-to-be-accessed
    content to improve cache hit rates.
    """
    
    def __init__(
        self,
        max_prefetch_workers: int = 4,
        prefetch_threshold: float = 0.7,
        pattern_window_hours: int = 24,
    ):
        """
        Initialize prefetch optimizer.
        
        Args:
            max_prefetch_workers: Maximum number of prefetch worker threads
            prefetch_threshold: Confidence threshold for prefetching
            pattern_window_hours: Window for analyzing access patterns
        """
        self.max_workers = max_prefetch_workers
        self.prefetch_threshold = prefetch_threshold
        self.pattern_window = timedelta(hours=pattern_window_hours)
        
        self._access_history: Dict[str, List[datetime]] = {}
        self._prefetch_executor = ThreadPoolExecutor(max_workers=max_prefetch_workers)
        self._prefetch_futures: Set[Future] = set()
        self._lock = threading.RLock()
    
    def optimize(self, cache_manager: 'CacheManager') -> Dict[str, Any]:
        """Optimize cache by prefetching likely content."""
        prefetch_candidates = self._identify_prefetch_candidates()
        prefetched_count = 0
        
        for candidate in prefetch_candidates:
            if self._should_prefetch(candidate, cache_manager):
                future = self._prefetch_executor.submit(
                    self._prefetch_content, candidate, cache_manager
                )
                self._prefetch_futures.add(future)
                prefetched_count += 1
        
        # Clean up completed futures
        self._cleanup_futures()
        
        return {
            'prefetch_candidates': len(prefetch_candidates),
            'prefetched_count': prefetched_count,
            'active_prefetch_tasks': len(self._prefetch_futures),
        }
    
    def record_access(self, key: str, timestamp: Optional[datetime] = None) -> None:
        """Record access for pattern analysis."""
        if timestamp is None:
            timestamp = datetime.now()
        
        with self._lock:
            if key not in self._access_history:
                self._access_history[key] = []
            
            self._access_history[key].append(timestamp)
            
            # Clean old data
            cutoff = timestamp - self.pattern_window
            self._access_history[key] = [
                t for t in self._access_history[key] if t >= cutoff
            ]
    
    def _identify_prefetch_candidates(self) -> List[Dict[str, Any]]:
        """Identify content that should be prefetched."""
        candidates = []
        now = datetime.now()
        
        with self._lock:
            for key, access_times in self._access_history.items():
                if len(access_times) < 2:
                    continue
                
                # Calculate access frequency
                recent_accesses = [t for t in access_times if now - t <= timedelta(hours=1)]
                frequency = len(recent_accesses)
                
                # Calculate access pattern regularity
                if len(access_times) >= 3:
                    intervals = []
                    for i in range(1, len(access_times)):
                        interval = (access_times[i] - access_times[i-1]).total_seconds()
                        intervals.append(interval)
                    
                    # Calculate coefficient of variation for regularity
                    if intervals:
                        mean_interval = sum(intervals) / len(intervals)
                        variance = sum((x - mean_interval) ** 2 for x in intervals) / len(intervals)
                        std_dev = variance ** 0.5
                        cv = std_dev / mean_interval if mean_interval > 0 else float('inf')
                        
                        # Lower CV means more regular access pattern
                        regularity_score = 1.0 / (1.0 + cv)
                    else:
                        regularity_score = 0.0
                else:
                    regularity_score = 0.0
                
                # Calculate overall prefetch score
                score = frequency * 0.6 + regularity_score * 0.4
                
                if score >= self.prefetch_threshold:
                    candidates.append({
                        'key': key,
                        'score': score,
                        'frequency': frequency,
                        'regularity': regularity_score,
                        'last_access': max(access_times),
                    })
        
        # Sort by score (highest first)
        candidates.sort(key=lambda c: c['score'], reverse=True)
        
        return candidates
    
    def _should_prefetch(self, candidate: Dict[str, Any], cache_manager: 'CacheManager') -> bool:
        """Determine if candidate should be prefetched."""
        # Check if already in cache
        if cache_manager.get(candidate['key']) is not None:
            return False
        
        # Check if prefetch is already in progress
        key_hash = hashlib.md5(candidate['key'].encode()).hexdigest()
        for future in self._prefetch_futures:
            if not future.done() and getattr(future, 'key_hash', None) == key_hash:
                return False
        
        return True
    
    def _prefetch_content(self, candidate: Dict[str, Any], cache_manager: 'CacheManager') -> None:
        """Prefetch content for candidate."""
        # This is a placeholder - in a real implementation, you would
        # regenerate the content based on the key information
        # For now, we'll just mark that prefetch was attempted
        pass
    
    def _cleanup_futures(self) -> None:
        """Clean up completed prefetch futures."""
        completed = {f for f in self._prefetch_futures if f.done()}
        self._prefetch_futures -= completed


class WarmupOptimizer(CacheOptimizer):
    """
    Cache warmup optimizer for preloading frequently used content.
    
    Preloads cache with commonly used diagrams and templates
    to improve initial performance.
    """
    
    def __init__(
        self,
        warmup_templates: Optional[List[str]] = None,
        warmup_diagrams: Optional[List[str]] = None,
        max_warmup_workers: int = 4,
    ):
        """
        Initialize warmup optimizer.
        
        Args:
            warmup_templates: List of template names to warm up
            warmup_diagrams: List of diagram codes to warm up
            max_warmup_workers: Maximum number of warmup worker threads
        """
        self.warmup_templates = warmup_templates or []
        self.warmup_diagrams = warmup_diagrams or []
        self.max_workers = max_warmup_workers
        
        self._warmup_executor = ThreadPoolExecutor(max_workers=max_warmup_workers)
    
    def optimize(self, cache_manager: 'CacheManager') -> Dict[str, Any]:
        """Optimize cache by warming up with common content."""
        warmed_count = 0
        futures = []
        
        # Warm up templates
        for template_name in self.warmup_templates:
            future = self._warmup_executor.submit(
                self._warmup_template, template_name, cache_manager
            )
            futures.append(future)
        
        # Warm up diagrams
        for diagram_code in self.warmup_diagrams:
            future = self._warmup_executor.submit(
                self._warmup_diagram, diagram_code, cache_manager
            )
            futures.append(future)
        
        # Wait for completion
        for future in futures:
            try:
                if future.result():
                    warmed_count += 1
            except Exception:
                # Ignore warmup failures
                pass
        
        return {
            'warmup_tasks': len(futures),
            'successful_warmups': warmed_count,
            'templates_warmed': len(self.warmup_templates),
            'diagrams_warmed': len(self.warmup_diagrams),
        }
    
    def _warmup_template(self, template_name: str, cache_manager: 'CacheManager') -> bool:
        """Warm up cache with template."""
        try:
            # This would generate common variations of the template
            # For now, just return success
            return True
        except Exception:
            return False
    
    def _warmup_diagram(self, diagram_code: str, cache_manager: 'CacheManager') -> bool:
        """Warm up cache with diagram."""
        try:
            # This would render the diagram in common formats
            # For now, just return success
            return True
        except Exception:
            return False
    
    def add_warmup_content(
        self,
        templates: Optional[List[str]] = None,
        diagrams: Optional[List[str]] = None,
    ) -> None:
        """Add content to warmup list."""
        if templates:
            self.warmup_templates.extend(templates)
        
        if diagrams:
            self.warmup_diagrams.extend(diagrams)


class AdaptiveOptimizer(CacheOptimizer):
    """
    Adaptive optimizer that combines multiple optimization strategies.
    
    Automatically selects and applies the most effective optimization
    strategies based on current cache performance and usage patterns.
    """
    
    def __init__(self):
        """Initialize adaptive optimizer."""
        self.optimizers = {
            'compression': CompressionOptimizer(),
            'prefetch': PrefetchOptimizer(),
            'warmup': WarmupOptimizer(),
        }
        
        self._optimization_history: List[Dict[str, Any]] = []
    
    def optimize(self, cache_manager: 'CacheManager') -> Dict[str, Any]:
        """Apply adaptive optimization strategies."""
        results = {}
        
        # Get current cache statistics
        cache_stats = cache_manager.get_statistics()
        
        # Determine which optimizations to apply
        optimizations_to_apply = self._select_optimizations(cache_stats)
        
        for opt_name in optimizations_to_apply:
            if opt_name in self.optimizers:
                try:
                    opt_result = self.optimizers[opt_name].optimize(cache_manager)
                    results[opt_name] = opt_result
                except Exception as e:
                    results[opt_name] = {'error': str(e)}
        
        # Record optimization results
        self._optimization_history.append({
            'timestamp': datetime.now().isoformat(),
            'cache_stats': cache_stats,
            'optimizations_applied': optimizations_to_apply,
            'results': results,
        })
        
        # Keep only recent history
        cutoff = datetime.now() - timedelta(days=7)
        self._optimization_history = [
            h for h in self._optimization_history
            if datetime.fromisoformat(h['timestamp']) >= cutoff
        ]
        
        return {
            'optimizations_applied': optimizations_to_apply,
            'results': results,
            'total_optimizations': len(optimizations_to_apply),
        }
    
    def _select_optimizations(self, cache_stats: Dict[str, Any]) -> List[str]:
        """Select which optimizations to apply based on cache state."""
        optimizations = []
        
        # Apply compression if cache is getting full
        if cache_stats.get('utilization', 0) > 0.7:
            optimizations.append('compression')
        
        # Apply prefetch if hit rate is low
        if cache_stats.get('hit_rate', 0) < 0.6:
            optimizations.append('prefetch')
        
        # Apply warmup if cache is mostly empty
        if cache_stats.get('entry_count', 0) < 10:
            optimizations.append('warmup')
        
        return optimizations
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """Get optimization performance report."""
        if not self._optimization_history:
            return {}
        
        # Calculate optimization effectiveness
        effectiveness = {}
        
        for opt_name in self.optimizers.keys():
            opt_results = []
            for history in self._optimization_history:
                if opt_name in history.get('optimizations_applied', []):
                    result = history['results'].get(opt_name, {})
                    if 'error' not in result:
                        opt_results.append(result)
            
            if opt_results:
                effectiveness[opt_name] = {
                    'applications': len(opt_results),
                    'average_benefit': self._calculate_average_benefit(opt_results),
                }
        
        return {
            'optimization_history_count': len(self._optimization_history),
            'optimization_effectiveness': effectiveness,
            'most_recent_optimization': self._optimization_history[-1] if self._optimization_history else None,
        }
    
    def _calculate_average_benefit(self, results: List[Dict[str, Any]]) -> float:
        """Calculate average benefit from optimization results."""
        # This is a simplified calculation - in practice, you'd want
        # more sophisticated metrics based on the specific optimization
        benefits = []
        
        for result in results:
            if 'space_saved_bytes' in result:
                benefits.append(result['space_saved_bytes'])
            elif 'prefetched_count' in result:
                benefits.append(result['prefetched_count'] * 1000)  # Estimate benefit
            elif 'successful_warmups' in result:
                benefits.append(result['successful_warmups'] * 500)  # Estimate benefit
        
        return sum(benefits) / len(benefits) if benefits else 0.0
