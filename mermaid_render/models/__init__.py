"""
Diagram model classes for the Mermaid Render library.

This package contains all the diagram model classes that provide
object-oriented interfaces for creating different types of Mermaid diagrams.

Available diagram types:
- FlowchartDiagram: Flowcharts and process diagrams
- SequenceDiagram: Sequence diagrams for interactions
- ClassDiagram: UML class diagrams
- StateDiagram: State machine diagrams
- ERDiagram: Entity-relationship diagrams
- UserJourneyDiagram: User journey mapping
- GanttDiagram: Project timeline diagrams
- PieChartDiagram: Pie charts for data visualization
- GitGraphDiagram: Git branching diagrams
- MindmapDiagram: Mind maps and hierarchical structures
"""

from .flowchart import FlowchartDiagram
from .sequence import SequenceDiagram
from .class_diagram import ClassDiagram
from .state import StateDiagram
from .er_diagram import ERDiagram
from .user_journey import UserJourneyDiagram
from .gantt import GanttDiagram
from .pie_chart import PieChartDiagram
from .git_graph import GitGraphDiagram
from .mindmap import MindmapDiagram

__all__ = [
    "FlowchartDiagram",
    "SequenceDiagram", 
    "ClassDiagram",
    "StateDiagram",
    "ERDiagram",
    "UserJourneyDiagram",
    "GanttDiagram",
    "PieChartDiagram",
    "GitGraphDiagram",
    "MindmapDiagram",
]
