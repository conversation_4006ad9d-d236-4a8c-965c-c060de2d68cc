"""
Template system for the Mermaid Render library.

This package provides a comprehensive template system for creating reusable
diagram templates with parameterized generation, template management,
and community template sharing.

Features:
- Pre-built templates for common diagram patterns
- Parameterized template generation
- Template validation and schema checking
- Template library management
- Custom template creation and sharing
- Integration with data sources (JSON, CSV, databases)

Example:
    >>> from mermaid_render.templates import TemplateManager, generate_from_template
    >>> 
    >>> # Use a built-in template
    >>> manager = TemplateManager()
    >>> diagram = manager.generate("software_architecture", {
    ...     "services": ["API", "Database", "Cache"],
    ...     "connections": [("API", "Database"), ("API", "Cache")]
    ... })
    >>> 
    >>> # Create custom template
    >>> template = manager.create_template("my_template", {
    ...     "type": "flowchart",
    ...     "parameters": {"title": "string", "nodes": "list"},
    ...     "template": "flowchart TD\\n    {{title}}\\n    {% for node in nodes %}{{node}}{% endfor %}"
    ... })
"""

from .template_manager import TemplateManager, Template
from .generators import (
    FlowchartGenerator,
    SequenceGenerator,
    ClassDiagramGenerator,
    ArchitectureGenerator,
    ProcessFlowGenerator,
)
from .schema import TemplateSchema, ParameterSchema, validate_template
from .library import BuiltInTemplates, CommunityTemplates
from .data_sources import (
    JSONDataSource,
    CSVDataSource,
    DatabaseDataSource,
    APIDataSource,
)

# Convenience functions
from .utils import (
    generate_from_template,
    list_available_templates,
    get_template_info,
    validate_template_parameters,
    export_template,
    import_template,
)

__all__ = [
    # Core classes
    "TemplateManager",
    "Template",
    "TemplateSchema",
    "ParameterSchema",
    
    # Generators
    "FlowchartGenerator",
    "SequenceGenerator", 
    "ClassDiagramGenerator",
    "ArchitectureGenerator",
    "ProcessFlowGenerator",
    
    # Template libraries
    "BuiltInTemplates",
    "CommunityTemplates",
    
    # Data sources
    "JSONDataSource",
    "CSVDataSource",
    "DatabaseDataSource",
    "APIDataSource",
    
    # Utilities
    "generate_from_template",
    "list_available_templates",
    "get_template_info",
    "validate_template_parameters",
    "validate_template",
    "export_template",
    "import_template",
]
