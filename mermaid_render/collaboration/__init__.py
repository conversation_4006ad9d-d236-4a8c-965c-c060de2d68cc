"""
Collaborative diagram editing and version control for Mermaid Render.

This package provides comprehensive collaboration features including
multi-user editing, version control, change tracking, conflict resolution,
and integration with Git workflows.

Features:
- Multi-user collaborative editing with real-time synchronization
- Version control with branching and merging capabilities
- Change tracking and history management
- Conflict resolution for simultaneous edits
- Comment and review system
- Integration with Git repositories
- User management and permissions
- Audit trails and activity logs

Example:
    >>> from mermaid_render.collaboration import CollaborationManager, VersionControl
    >>> 
    >>> # Setup collaboration
    >>> collab = CollaborationManager()
    >>> version_control = VersionControl()
    >>> 
    >>> # Create collaborative session
    >>> session = collab.create_session("my_diagram", "flowchart")
    >>> 
    >>> # Add collaborators
    >>> collab.add_collaborator(session.id, "<EMAIL>", "editor")
    >>> 
    >>> # Track changes
    >>> version_control.commit_changes(session.id, "Added new nodes", "<EMAIL>")
"""

from .collaboration_manager import (
    CollaborationManager,
    CollaborativeSession,
    Collaborator,
    Permission,
    SessionState,
)
from .version_control import (
    VersionControl,
    DiagramVersion,
    ChangeSet,
    Commit,
    Branch,
    MergeResult,
)
from .diff_engine import (
    DiffEngine,
    DiagramDiff,
    Change,
    ChangeType,
    ConflictResolution,
)
from .merge_resolver import (
    MergeResolver,
    MergeStrategy,
    ConflictResolver,
    MergeConflict,
)
from .comments import (
    CommentSystem,
    Comment,
    CommentThread,
    Review,
    ReviewStatus,
)
from .activity_log import (
    ActivityLogger,
    Activity,
    ActivityType,
    AuditTrail,
)

# Convenience functions
from .utils import (
    create_collaborative_session,
    invite_collaborator,
    commit_diagram_changes,
    create_branch,
    merge_branches,
    resolve_conflicts,
    add_comment,
    get_activity_log,
)

__all__ = [
    # Core collaboration
    "CollaborationManager",
    "CollaborativeSession",
    "Collaborator",
    "Permission",
    "SessionState",
    
    # Version control
    "VersionControl",
    "DiagramVersion",
    "ChangeSet",
    "Commit",
    "Branch",
    "MergeResult",
    
    # Diff and merge
    "DiffEngine",
    "DiagramDiff",
    "Change",
    "ChangeType",
    "ConflictResolution",
    "MergeResolver",
    "MergeStrategy",
    "ConflictResolver",
    "MergeConflict",
    
    # Comments and reviews
    "CommentSystem",
    "Comment",
    "CommentThread",
    "Review",
    "ReviewStatus",
    
    # Activity logging
    "ActivityLogger",
    "Activity",
    "ActivityType",
    "AuditTrail",
    
    # Utilities
    "create_collaborative_session",
    "invite_collaborator",
    "commit_diagram_changes",
    "create_branch",
    "merge_branches",
    "resolve_conflicts",
    "add_comment",
    "get_activity_log",
]
