"""
Export utilities for the Mermaid Render library.

This module provides convenient functions for exporting diagrams to various formats.
"""

from typing import Optional, Dict, Any, Union
from pathlib import Path
from ..core import MermaidRenderer, MermaidDiagram
from ..exceptions import UnsupportedFormatError, RenderingError


def export_to_file(
    diagram: Union[MermaidDiagram, str],
    output_path: Union[str, Path],
    format: Optional[str] = None,
    theme: Optional[str] = None,
    config: Optional[Dict[str, Any]] = None,
    renderer: Optional[MermaidRenderer] = None,
    **options: Any,
) -> None:
    """
    Export a Mermaid diagram to file.
    
    This is a convenience function that handles the complete export process
    including format detection, rendering, and file writing.
    
    Args:
        diagram: MermaidDiagram object or raw Mermaid syntax
        output_path: Output file path
        format: Output format (auto-detected from extension if not provided)
        theme: Optional theme name
        config: Optional configuration dictionary
        renderer: Optional custom renderer instance
        **options: Additional rendering options
        
    Raises:
        UnsupportedFormatError: If format is not supported
        RenderingError: If rendering fails
        
    Example:
        >>> from mermaid_render import FlowchartDiagram, export_to_file
        >>> 
        >>> # Create a diagram
        >>> flowchart = FlowchartDiagram()
        >>> flowchart.add_node("A", "Start")
        >>> flowchart.add_node("B", "End")
        >>> flowchart.add_edge("A", "B")
        >>> 
        >>> # Export to various formats
        >>> export_to_file(flowchart, "diagram.svg")
        >>> export_to_file(flowchart, "diagram.png", theme="dark")
        >>> export_to_file(flowchart, "diagram.pdf", config={"width": 1200})
    """
    output_path = Path(output_path)
    
    # Auto-detect format from file extension if not provided
    if format is None:
        format = _detect_format_from_extension(output_path)
    
    # Create renderer if not provided
    if renderer is None:
        from ..core import MermaidConfig
        renderer_config = MermaidConfig()
        if config:
            renderer_config.update(config)
        renderer = MermaidRenderer(config=renderer_config)
    
    # Set theme if provided
    if theme:
        renderer.set_theme(theme)
    
    # Render and save
    renderer.save(diagram, output_path, format, **options)


def export_multiple_formats(
    diagram: Union[MermaidDiagram, str],
    base_path: Union[str, Path],
    formats: list[str],
    theme: Optional[str] = None,
    config: Optional[Dict[str, Any]] = None,
    **options: Any,
) -> Dict[str, Path]:
    """
    Export a diagram to multiple formats.
    
    Args:
        diagram: MermaidDiagram object or raw Mermaid syntax
        base_path: Base path for output files (without extension)
        formats: List of formats to export to
        theme: Optional theme name
        config: Optional configuration dictionary
        **options: Additional rendering options
        
    Returns:
        Dictionary mapping format to output file path
        
    Example:
        >>> paths = export_multiple_formats(
        ...     diagram,
        ...     "my_diagram",
        ...     ["svg", "png", "pdf"]
        ... )
        >>> # Returns: {"svg": Path("my_diagram.svg"), ...}
    """
    base_path = Path(base_path)
    output_paths = {}
    
    # Create renderer
    from ..core import MermaidConfig, MermaidRenderer
    renderer_config = MermaidConfig()
    if config:
        renderer_config.update(config)
    renderer = MermaidRenderer(config=renderer_config)
    
    if theme:
        renderer.set_theme(theme)
    
    # Export to each format
    for fmt in formats:
        output_path = base_path.with_suffix(f".{fmt}")
        renderer.save(diagram, output_path, fmt, **options)
        output_paths[fmt] = output_path
    
    return output_paths


def batch_export(
    diagrams: Dict[str, Union[MermaidDiagram, str]],
    output_dir: Union[str, Path],
    format: str = "svg",
    theme: Optional[str] = None,
    config: Optional[Dict[str, Any]] = None,
    **options: Any,
) -> Dict[str, Path]:
    """
    Export multiple diagrams to files.
    
    Args:
        diagrams: Dictionary mapping names to diagrams
        output_dir: Output directory
        format: Output format for all diagrams
        theme: Optional theme name
        config: Optional configuration dictionary
        **options: Additional rendering options
        
    Returns:
        Dictionary mapping diagram names to output file paths
        
    Example:
        >>> diagrams = {
        ...     "flowchart": my_flowchart,
        ...     "sequence": my_sequence_diagram,
        ... }
        >>> paths = batch_export(diagrams, "output/", "png")
    """
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    output_paths = {}
    
    # Create renderer
    from ..core import MermaidConfig, MermaidRenderer
    renderer_config = MermaidConfig()
    if config:
        renderer_config.update(config)
    renderer = MermaidRenderer(config=renderer_config)
    
    if theme:
        renderer.set_theme(theme)
    
    # Export each diagram
    for name, diagram in diagrams.items():
        # Sanitize filename
        safe_name = _sanitize_filename(name)
        output_path = output_dir / f"{safe_name}.{format}"
        
        renderer.save(diagram, output_path, format, **options)
        output_paths[name] = output_path
    
    return output_paths


def _detect_format_from_extension(file_path: Path) -> str:
    """
    Detect output format from file extension.
    
    Args:
        file_path: File path to analyze
        
    Returns:
        Detected format
        
    Raises:
        UnsupportedFormatError: If extension is not recognized
    """
    extension = file_path.suffix.lower().lstrip('.')
    
    if not extension:
        raise UnsupportedFormatError("No file extension provided and format not specified")
    
    # Map extensions to formats
    extension_map = {
        'svg': 'svg',
        'png': 'png',
        'pdf': 'pdf',
        'jpg': 'png',  # Treat as PNG for now
        'jpeg': 'png',
    }
    
    if extension not in extension_map:
        supported = list(extension_map.keys())
        raise UnsupportedFormatError(
            f"Unsupported file extension: .{extension}",
            requested_format=extension,
            supported_formats=supported
        )
    
    return extension_map[extension]


def _sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for safe file system usage.
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    import re
    
    # Replace invalid characters with underscores
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing whitespace and dots
    sanitized = sanitized.strip(' .')
    
    # Ensure it's not empty
    if not sanitized:
        sanitized = "diagram"
    
    return sanitized
