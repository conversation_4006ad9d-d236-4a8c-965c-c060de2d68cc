"""
Core classes for the Mermaid Render library.

This module contains the main classes that form the foundation of the library:
- MermaidRenderer: Main rendering engine
- MermaidDiagram: Base class for all diagram types
- MermaidTheme: Theme configuration
- MermaidConfig: Global configuration management
"""

import os
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
import mermaid as md

from .exceptions import (
    RenderingError,
    UnsupportedFormatError,
    ConfigurationError,
    ValidationError,
)


class MermaidConfig:
    """
    Configuration management for Mermaid rendering.
    
    Handles global settings, server configuration, and rendering options.
    """
    
    def __init__(self, **kwargs: Any) -> None:
        """
        Initialize configuration with default values.
        
        Args:
            **kwargs: Configuration options to override defaults
        """
        self._config: Dict[str, Any] = {
            "server_url": os.getenv("MERMAID_INK_SERVER", "https://mermaid.ink"),
            "timeout": 30,
            "retries": 3,
            "default_theme": "default",
            "default_format": "svg",
            "validate_syntax": True,
            "cache_enabled": True,
            "cache_dir": Path.home() / ".mermaid_render_cache",
        }
        self._config.update(kwargs)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value."""
        return self._config.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value."""
        self._config[key] = value
    
    def update(self, config: Dict[str, Any]) -> None:
        """Update multiple configuration values."""
        self._config.update(config)
    
    def to_dict(self) -> Dict[str, Any]:
        """Return configuration as dictionary."""
        return self._config.copy()


class MermaidTheme:
    """
    Theme configuration for Mermaid diagrams.
    
    Manages color schemes, fonts, and styling options.
    """
    
    BUILT_IN_THEMES = {
        "default": {},
        "dark": {"theme": "dark"},
        "forest": {"theme": "forest"},
        "neutral": {"theme": "neutral"},
        "base": {"theme": "base"},
    }
    
    def __init__(self, name: str = "default", **custom_config: Any) -> None:
        """
        Initialize theme configuration.
        
        Args:
            name: Built-in theme name or "custom" for custom themes
            **custom_config: Custom theme configuration options
        """
        self.name = name
        self._config: Dict[str, Any] = {}
        
        if name in self.BUILT_IN_THEMES:
            self._config = self.BUILT_IN_THEMES[name].copy()
        elif name == "custom":
            self._config = {}
        else:
            raise ConfigurationError(f"Unknown theme: {name}")
        
        self._config.update(custom_config)
    
    def to_dict(self) -> Dict[str, Any]:
        """Return theme configuration as dictionary."""
        return self._config.copy()
    
    def apply_to_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Apply theme settings to a configuration dictionary."""
        result = config.copy()
        result.update(self._config)
        return result


class MermaidDiagram(ABC):
    """
    Abstract base class for all Mermaid diagram types.
    
    Provides common functionality for diagram creation, validation, and rendering.
    """
    
    def __init__(self, title: Optional[str] = None) -> None:
        """
        Initialize diagram with optional title.
        
        Args:
            title: Optional diagram title
        """
        self.title = title
        self._elements: List[str] = []
        self._config: Dict[str, Any] = {}
    
    @abstractmethod
    def get_diagram_type(self) -> str:
        """Return the Mermaid diagram type identifier."""
        pass
    
    @abstractmethod
    def to_mermaid(self) -> str:
        """Generate Mermaid syntax for this diagram."""
        pass
    
    def add_config(self, key: str, value: Any) -> None:
        """Add configuration option to the diagram."""
        self._config[key] = value
    
    def get_config(self) -> Dict[str, Any]:
        """Get diagram configuration."""
        return self._config.copy()
    
    def validate(self) -> bool:
        """
        Validate the diagram syntax.
        
        Returns:
            True if diagram is valid, False otherwise
        """
        from .validators import MermaidValidator
        
        validator = MermaidValidator()
        result = validator.validate(self.to_mermaid())
        return result.is_valid
    
    def __str__(self) -> str:
        """Return Mermaid syntax representation."""
        return self.to_mermaid()


class MermaidRenderer:
    """
    Main rendering engine for Mermaid diagrams.
    
    Handles rendering to various formats, theme application, and output management.
    """
    
    SUPPORTED_FORMATS = ["svg", "png", "pdf"]
    
    def __init__(
        self,
        config: Optional[MermaidConfig] = None,
        theme: Optional[Union[str, MermaidTheme]] = None,
    ) -> None:
        """
        Initialize the renderer.
        
        Args:
            config: Optional configuration object
            theme: Optional theme name or theme object
        """
        self.config = config or MermaidConfig()
        self._theme: Optional[MermaidTheme] = None
        
        if theme:
            self.set_theme(theme)
    
    def set_theme(self, theme: Union[str, MermaidTheme]) -> None:
        """
        Set the rendering theme.
        
        Args:
            theme: Theme name or MermaidTheme object
        """
        if isinstance(theme, str):
            self._theme = MermaidTheme(theme)
        elif isinstance(theme, MermaidTheme):
            self._theme = theme
        else:
            raise ConfigurationError(f"Invalid theme type: {type(theme)}")
    
    def get_theme(self) -> Optional[MermaidTheme]:
        """Get current theme."""
        return self._theme
    
    def render(
        self,
        diagram: Union[MermaidDiagram, str],
        format: str = "svg",
        **options: Any,
    ) -> str:
        """
        Render a diagram to the specified format.
        
        Args:
            diagram: MermaidDiagram object or raw Mermaid syntax
            format: Output format (svg, png, pdf)
            **options: Additional rendering options
            
        Returns:
            Rendered diagram content
            
        Raises:
            UnsupportedFormatError: If format is not supported
            RenderingError: If rendering fails
            ValidationError: If diagram is invalid
        """
        if format not in self.SUPPORTED_FORMATS:
            raise UnsupportedFormatError(f"Unsupported format: {format}")
        
        # Get Mermaid syntax
        if isinstance(diagram, MermaidDiagram):
            mermaid_code = diagram.to_mermaid()
            
            # Validate if enabled
            if self.config.get("validate_syntax", True):
                if not diagram.validate():
                    raise ValidationError("Invalid diagram syntax")
        else:
            mermaid_code = diagram
            
            # Validate raw syntax if enabled
            if self.config.get("validate_syntax", True):
                from .validators import MermaidValidator
                validator = MermaidValidator()
                result = validator.validate(mermaid_code)
                if not result.is_valid:
                    raise ValidationError(f"Invalid syntax: {result.errors}")
        
        return self.render_raw(mermaid_code, format, **options)
    
    def render_raw(self, mermaid_code: str, format: str = "svg", **options: Any) -> str:
        """
        Render raw Mermaid code to specified format.
        
        Args:
            mermaid_code: Raw Mermaid diagram syntax
            format: Output format
            **options: Additional rendering options
            
        Returns:
            Rendered content
        """
        try:
            # Create Mermaid object with theme if available
            config = {}
            if self._theme:
                config = self._theme.to_dict()
            config.update(options)
            
            # Use mermaid-py for rendering
            mermaid_obj = md.Mermaid(mermaid_code)
            
            if format == "svg":
                # For SVG, we can get the content directly
                return str(mermaid_obj)
            else:
                # For other formats, we need to use the mermaid.ink service
                # This is a simplified implementation - in production you'd want
                # more sophisticated handling
                raise UnsupportedFormatError(f"Format {format} not yet implemented")
                
        except Exception as e:
            raise RenderingError(f"Failed to render diagram: {str(e)}") from e
    
    def save(
        self,
        diagram: Union[MermaidDiagram, str],
        output_path: Union[str, Path],
        format: Optional[str] = None,
        **options: Any,
    ) -> None:
        """
        Render and save diagram to file.
        
        Args:
            diagram: Diagram to render
            output_path: Output file path
            format: Output format (inferred from extension if not provided)
            **options: Additional rendering options
        """
        output_path = Path(output_path)
        
        # Infer format from extension if not provided
        if format is None:
            format = output_path.suffix.lstrip('.').lower()
            if not format:
                format = self.config.get("default_format", "svg")
        
        # Render the diagram
        content = self.render(diagram, format, **options)
        
        # Create output directory if it doesn't exist
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Write to file
        mode = 'w' if format == 'svg' else 'wb'
        with open(output_path, mode) as f:
            if format == 'svg':
                f.write(content)
            else:
                # For binary formats, content should be bytes
                if isinstance(content, str):
                    f.write(content.encode())
                else:
                    f.write(content)
