# Mermaid Render - Comprehensive Edition

[![PyPI version](https://badge.fury.io/py/mermaid-render.svg)](https://badge.fury.io/py/mermaid-render)
[![Python Support](https://img.shields.io/pypi/pyversions/mermaid-render.svg)](https://pypi.org/project/mermaid-render/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

A comprehensive Python library for rendering, validating, and managing Mermaid diagrams with advanced features including interactive building, collaboration, AI-powered generation, and enterprise-grade caching.

## 🚀 Features

### Core Rendering & Validation
- **Multiple Rendering Backends**: Support for Playwright, Puppeteer, and CLI-based rendering
- **Comprehensive Validation**: Syntax validation, semantic analysis, and best practice recommendations
- **Rich Export Options**: Export to SVG, PNG, PDF, and other formats with customizable styling
- **Template System**: Pre-built templates and generators for common diagram types

### 🎨 Interactive Diagram Builder
- **Visual Drag-and-Drop Interface**: Build diagrams visually with real-time preview
- **Live Code Generation**: Automatic Mermaid code generation from visual elements
- **Real-time Collaboration**: Multi-user editing with WebSocket synchronization
- **Template Library**: Quick start with pre-built diagram templates

### 🤝 Collaboration & Version Control
- **Multi-user Editing**: Real-time collaborative diagram editing
- **Version Control**: Git-like branching, merging, and change tracking
- **Comment System**: Add comments and reviews to diagrams
- **Conflict Resolution**: Intelligent merge conflict resolution
- **Activity Logging**: Comprehensive audit trails and activity logs

### 🤖 AI-Powered Features
- **Natural Language Generation**: Create diagrams from text descriptions
- **Intelligent Optimization**: AI-powered layout and style optimization
- **Smart Suggestions**: Get AI recommendations for diagram improvements
- **Quality Analysis**: Automated diagram quality assessment
- **Multiple AI Providers**: Support for OpenAI, Anthropic, and local models

### ⚡ Performance & Caching
- **Multi-level Caching**: Memory, file, and Redis-based caching
- **Performance Monitoring**: Real-time performance metrics and optimization
- **Intelligent Eviction**: Smart cache management with multiple strategies
- **Compression**: Automatic content compression for storage efficiency

## 📦 Installation

```bash
pip install mermaid-render
```

For additional features:
```bash
# For interactive features
pip install mermaid-render[interactive]

# For collaboration features
pip install mermaid-render[collaboration]

# For AI features
pip install mermaid-render[ai]

# For caching with Redis
pip install mermaid-render[cache]

# For all features
pip install mermaid-render[all]
```

## 🚀 Quick Start

### Basic Rendering

```python
from mermaid_render import MermaidRenderer

# Create renderer
renderer = MermaidRenderer()

# Render a simple diagram
diagram_code = """
flowchart TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
    C --> E[End]
    D --> E
"""

# Render to SVG
svg_output = renderer.render(diagram_code, format="svg")

# Save to file
renderer.save(diagram_code, "diagram.png", format="png")
```

### AI-Powered Generation

```python
from mermaid_render.ai import generate_from_text

# Generate diagram from natural language
result = generate_from_text(
    "Create a flowchart showing the user login process with validation and error handling"
)

print(result['diagram_code'])
# Output: Complete Mermaid flowchart code
```

### Interactive Builder

```python
from mermaid_render.interactive import start_server

# Start interactive diagram builder
start_server(host="localhost", port=8080)
# Access at http://localhost:8080
```

### Collaboration

```python
from mermaid_render.collaboration import create_collaborative_session

# Create collaborative session
session_id = create_collaborative_session(
    diagram_id="my_diagram",
    diagram_type="flowchart",
    title="Team Process Diagram",
    owner_id="user123",
    owner_email="<EMAIL>",
    owner_name="John Doe"
)

print(f"Collaboration session created: {session_id}")
```

### Advanced Caching

```python
from mermaid_render.cache import create_cache_manager

# Create cache with Redis backend
cache = create_cache_manager(
    backend_type='redis',
    host='localhost',
    port=6379,
    max_size_mb=500
)

# Cache is automatically used by renderers
renderer = MermaidRenderer(cache_manager=cache)
```

## 📚 Advanced Examples

### Template-based Generation

```python
from mermaid_render.templates import FlowchartGenerator

# Generate flowchart from data
generator = FlowchartGenerator()
diagram = generator.from_steps([
    {"id": "start", "label": "Start Process", "type": "start"},
    {"id": "validate", "label": "Validate Input", "type": "process"},
    {"id": "decision", "label": "Valid?", "type": "decision"},
    {"id": "process", "label": "Process Data", "type": "process"},
    {"id": "error", "label": "Show Error", "type": "process"},
    {"id": "end", "label": "End", "type": "end"}
])

print(diagram.to_mermaid())
```

### AI Analysis and Optimization

```python
from mermaid_render.ai import analyze_diagram, get_suggestions, optimize_diagram

# Analyze diagram quality
analysis = analyze_diagram(diagram_code)
print(f"Quality Score: {analysis['quality']['overall_score']}")
print(f"Complexity: {analysis['complexity']['complexity_level']}")

# Get improvement suggestions
suggestions = get_suggestions(diagram_code)
for suggestion in suggestions:
    print(f"- {suggestion['title']}: {suggestion['description']}")

# Optimize diagram
optimizations = optimize_diagram(diagram_code, ["layout", "style"])
optimized_code = optimizations[0]['optimized_diagram']
```

### Real-time Collaboration

```python
from mermaid_render.collaboration import CollaborationManager

# Setup collaboration
collab = CollaborationManager()

# Create session
session = collab.create_session(
    diagram_id="team_diagram",
    diagram_type="flowchart",
    title="Team Workflow",
    owner_id="user1",
    owner_email="<EMAIL>",
    owner_name="Alice"
)

# Add collaborators
collab.add_collaborator(
    session.session_id,
    user_id="user2",
    email="<EMAIL>",
    name="Bob",
    permission="editor"
)
```

## 🏗️ Architecture

Mermaid Render is built with a modular architecture:

```
mermaid_render/
├── core/           # Core rendering and validation
├── models/         # Diagram models and data structures
├── renderers/      # Multiple rendering backends
├── validators/     # Validation and analysis
├── templates/      # Template system and generators
├── cache/          # Caching system with multiple backends
├── interactive/    # Web-based interactive builder
├── collaboration/  # Real-time collaboration features
├── ai/            # AI-powered generation and optimization
└── utils/         # Utilities and helpers
```

## 🔧 Configuration

### Environment Variables

```bash
# AI Provider Configuration
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_DEFAULT_TTL=3600

# Interactive Server
INTERACTIVE_HOST=localhost
INTERACTIVE_PORT=8080
```

## 📊 Performance

Mermaid Render is optimized for performance:

- **Caching**: Multi-level caching reduces rendering time by up to 90%
- **Async Support**: Full async/await support for non-blocking operations
- **Batch Processing**: Efficient batch rendering for multiple diagrams
- **Memory Management**: Intelligent memory usage and cleanup
- **Monitoring**: Built-in performance monitoring and metrics

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

```bash
# Clone repository
git clone https://github.com/yourusername/mermaid-render.git
cd mermaid-render

# Install development dependencies
pip install -e ".[dev,all]"

# Run tests
pytest

# Run linting
ruff check .
black .
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Mermaid](https://mermaid-js.github.io/) - The amazing diagramming language
- [Playwright](https://playwright.dev/) - Browser automation
- [FastAPI](https://fastapi.tiangolo.com/) - Web framework for interactive features
- All contributors and the open-source community
